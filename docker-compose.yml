# ========================================
# DailyInfo Docker Compose Configuration
# Enterprise AI-Powered News Monitoring System
# ========================================

services:
  dailyinfo:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: dailyinfo-app
    restart: unless-stopped
    
    # Load environment variables from .env file
    env_file:
      - .env

    # Additional environment variables (override .env file if needed)
    environment:
      # Timezone - adjust to your location
      - TZ=Asia/Shanghai

      # Performance settings - RSS async control
      - RSS_ASYNC_ENABLED=${RSS_ASYNC_ENABLED:-true}      # RSS异步收集（保留）
      - RSS_CONCURRENCY=${RSS_CONCURRENCY:-10}            # RSS并发数量
      
    # Volume mounts
    volumes:
      # Configuration files (create these from templates)
      - ./config.yaml:/app/config.yaml:ro
      - ./rss_feeds.yaml:/app/rss_feeds.yaml:ro
      
      # Persistent data
      - dailyinfo-logs:/app/logs
      - dailyinfo-data:/app/data
    
    # Resource limits - adjust based on your server capacity
    deploy:
      resources:
        limits:
          memory: 1G          # Maximum memory usage
          cpus: '1.0'         # Maximum CPU usage
        reservations:
          memory: 512M        # Reserved memory
          cpus: '0.5'         # Reserved CPU
    
    # Health check - improved for daily execution pattern and timezone handling
    healthcheck:
      test: ["CMD-SHELL", "/app/health_check.sh"]
      interval: 2m
      timeout: 30s
      retries: 3
      start_period: 2m
    
    # Logging configuration - 优化日志管理
    logging:
      driver: "json-file"
      options:
        max-size: "5m"     # 减小单文件大小到5MB
        max-file: "5"      # 增加保留文件数到5个
        compress: "true"   # 启用日志压缩
    
    # Uncomment if you need web interface access
    # ports:
    #   - "8080:8080"
    
    # Labels for management
    labels:
      - "com.dailyinfo.service=main"
      - "com.dailyinfo.version=2.0.0"
      - "com.dailyinfo.description=Enterprise AI-Powered News Monitoring System"

# Named volumes for data persistence
volumes:
  dailyinfo-logs:
    driver: local
    labels:
      - "com.dailyinfo.volume=logs"
  
  dailyinfo-data:
    driver: local
    labels:
      - "com.dailyinfo.volume=data"

# Optional: Custom network for multi-service deployments
# networks:
#   dailyinfo-network:
#     driver: bridge
