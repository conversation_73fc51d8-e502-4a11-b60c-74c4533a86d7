#!/usr/bin/env python3
"""
用于cron调度的检查脚本，判断是否应执行主任务。
"""

import os
import sys
from datetime import datetime
import yaml
import pwd
import grp
import subprocess

CONFIG_FILE = "/app/config.yaml"
LAST_RUN_FILE = "/app/data/last_run_date"
LOG_FILE = "/app/logs/cron_check.log"

def log_message(message, level="INFO"):
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    with open(LOG_FILE, "a") as f:
        f.write(f"[{timestamp}] [{level}] {message}\n")
        f.flush()  # 强制刷新文件缓冲区
    print(f"[{timestamp}] [{level}] {message}") # Also print to stdout for docker logs
    sys.stdout.flush()  # 强制刷新输出缓冲区

def drop_privileges(user, group):
    if os.geteuid() != 0:
        # Not running as root, nothing to do
        return

    try:
        uid = pwd.getpwnam(user).pw_uid
        gid = grp.getgrnam(group).gr_gid
    except KeyError:
        log_message(f"用户或组不存在: {user}:{group}", level="ERROR")
        sys.exit(1)

    os.setgid(gid)
    os.setgroups([]) # Clear supplementary groups
    os.setuid(uid)
    log_message(f"已切换到用户: {user} (UID: {uid}, GID: {gid})")

def main():
    # 确保data目录存在
    os.makedirs(os.path.dirname(LAST_RUN_FILE), exist_ok=True)
    # 确保logs目录存在
    os.makedirs(os.path.dirname(LOG_FILE), exist_ok=True)

    # 切换用户
    drop_privileges("dailyinfo", "dailyinfo")

    # 读取配置文件
    try:
        with open(CONFIG_FILE, 'r') as f:
            config = yaml.safe_load(f)
        run_time_str = config.get('daily_run_time', '06:00')
        log_message(f"从 {CONFIG_FILE} 读取到配置时间: '{run_time_str}'")
    except (IOError, yaml.YAMLError) as e:
        log_message(f"无法读取或解析配置文件: {e}", level="ERROR")
        sys.exit(1)

    # 获取当前时间和日期
    now = datetime.now()
    current_time_str = now.strftime("%H:%M")
    current_date_str = now.strftime("%Y-%m-%d")

    # 读取上次运行日期
    last_run_date = ""
    if os.path.exists(LAST_RUN_FILE):
        with open(LAST_RUN_FILE, 'r') as f:
            last_run_date = f.read().strip()
    log_message(f"从 {LAST_RUN_FILE} 读取到上次运行时间: '{last_run_date}'")

    # 检查时间（移除日期限制，允许同一天多次执行）
    # 只在时间匹配时输出详细日志，减少冗余信息
    if current_time_str == run_time_str:
        log_message(f"时间匹配！当前时间: {current_time_str}, 配置时间: {run_time_str}, 上次运行: {last_run_date}")
        log_message(f"时间匹配，开始执行任务...")
        
        # 使用subprocess.Popen来执行，实现实时输出
        command = [sys.executable, "/app/main.py", "run"]
        log_message(f"执行命令: {' '.join(command)}")
        try:
            # 使用Popen实现实时输出
            process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,  # 合并stderr到stdout
                text=True,
                bufsize=1,  # 行缓冲
                universal_newlines=True
            )

            # 实时读取并输出
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    # 直接输出，不使用log_message以避免重复时间戳
                    print(output.strip())
                    sys.stdout.flush()

            # 等待进程完成
            return_code = process.poll()

            # 如果主任务成功，则更新运行时间戳
            if return_code == 0:
                current_timestamp = now.strftime("%Y-%m-%d %H:%M:%S")
                with open(LAST_RUN_FILE, 'w') as f:
                    f.write(current_timestamp)
                log_message(f"任务成功执行并记录时间戳: '{current_timestamp}'.")
            else:
                log_message(f"主任务执行失败，返回码: {return_code}", level="ERROR")
        except Exception as e:
            log_message(f"执行主任务时发生异常: {e}", level="ERROR")
    else:
        # 时间不匹配时，只在整点或半点输出简化日志，避免每分钟都输出
        if current_time_str.endswith(":00") or current_time_str.endswith(":30"):
            log_message(f"等待执行时间 {run_time_str}，当前时间 {current_time_str}")

if __name__ == "__main__":
    main()
