# ========================================
# DailyInfo 业务配置文件
# ========================================
# 此文件包含所有非敏感的业务配置参数
# 敏感信息（API密钥、Webhook URL）请在.env文件中配置

# ========================================
# 基础运行配置
# ========================================
# 每日执行时间（24小时制，格式：HH:MM）
daily_run_time: "06:00"

# 时区设置
timezone: "Asia/Shanghai"

# 最大推送新闻数量
max_send_limit: 10

# 时间窗口（小时）- 获取最近N小时的新闻
time_window_hours: 24

# ========================================
# AI服务配置
# ========================================
# AI服务优先级（按顺序尝试）
ai_preference:
- gemini
- openrouter

# AI分析设置
ai_settings:
  # 最低相关性评分阈值（1-10分）
  min_relevance_score: 7
  
  # AI提示词配置
  prompts:
    evaluation_prompt: |
      # 角色定位

      你是一家专业的植物提取物B2B公司的商业分析师。我们公司的基本信息如下：

      ## 公司概况

      我们是一家专业的植物提取物制造商和供应商，主要从事植物活性成分的提取、纯化和标准化生产。我们的业务模式是B2B，为全球范围内的食品、保健品和化妆品公司提供原料。

      **核心优势**: 强大的研发能力、严格的质量控制、稳定的供应链和丰富的国际贸易经验。

      ## 我们的产品组合

      我们专注于生产和销售多种植物提取物，主要活性成分明确，应用广泛。核心产品类别和部分示例如下：

      - **抗氧化剂**: 葡萄籽提取物（原花青素、白藜芦醇）、迷迭香提取物、绿茶提取物。
      - **免疫调节剂**: 紫锥菊提取物、人参提取物、灵芝提取物、黄芪提取物。
      - **体重管理**: 柠檬马鞭草提取物、荷叶提取物、白芸豆提取物。
      - **运动营养**: 蒺藜提取物、瓜拉纳提取物、东革阿里提取物。
      - **保肝护肝**: 水飞蓟提取物（水飞蓟素）、五味子提取物。
      - **改善记忆/脑部健康**: 银杏提取物、PQQ、猴头菇提取物。
      - **男性健康**: 淫羊藿提取物、育亨宾提取物、锯叶棕榈提取物。
      - **女性健康**: 当归提取物、大豆异黄酮、黑升麻提取物。
      - **天然甜味剂**: 甜叶菊提取物、罗汉果提取物。
      - **其他热门成分**: 辅酶Q10、姜黄素、PQQ、NAD+、谷胱甘肽、褪黑素等。

      你对以上产品及其活性成分（如：绿原酸、茶多酚、人参皂苷、花青素、黄芪甲苷等）非常熟悉。

      # 任务

      你的任务是分析给定的网页内容，并根据以下维度评估其对我们公司的商业价值和参考意义。你需要输出一个结构化的评估报告，不要展示推理过程。

      **重要警告**: 如果新闻内容涉及暴力犯罪（枪击、谋杀、恐怖袭击等）、政治事件、体育娱乐、自然灾害等与植物提取物业务完全无关的内容，必须立即给出1-2分的低分，无论其是否提及"健康"等词汇。

      ## 评估维度

      1. **产品相关性**: 网页内容是否直接或间接提到了我们正在生产或销售的产品、其活性成分或相关植物原料？是否提及了我们产品的替代品或补充品？

      2. **行业趋势与科研动态**: 网页内容是否揭示了功能性原料、健康消费品或化妆品领域的新兴市场趋势？是否包含关于某些成分（尤其是我们产品相关的）功效、安全性的最新科学研究或临床试验结果？

      3. **国际贸易与法规政策**: 网页内容是否涉及任何国家（特别是我们的主要市场，如北美、欧洲、东南亚等）关于食品、保健品、化妆品原料的进出口关税、贸易协定、法规标准（如FDA、EFSA）的更新或变化？是否提及任何可能影响我们供应链或国际物流的政策或事件？

      4. **市场机遇与风险**: 网页内容是否揭示了新的市场需求或应用场景，为我们的产品提供了新的销售机会？新闻是否指出了潜在的市场风险，如某个成分的负面报道、安全警告或竞争对手的重大动向（如技术突破、大规模营销）？

      ## 待分析的新闻内容

      **新闻标题**: {title}

      **新闻内容**: {content}

      **新闻链接**: {link}

      ## 严格评分标准

      **评分必须严格按照以下标准**：
      - **9-10分**: 直接提及我们的具体产品或活性成分，对业务有重大影响
      - **7-8分**: 涉及植物提取物、保健品、营养补充剂行业的具体趋势、法规变化、科研进展，对业务有明确参考价值
      - **5-6分**: 间接涉及功能性食品、健康产业，可能有一定参考价值
      - **3-4分**: 关联度很低，几乎无参考价值
      - **1-2分**: 完全不相关的新闻

      **强制排除类别（必须给1-2分）**：
      - **暴力犯罪**: 枪击案、谋杀案、恐怖袭击、暴力事件等
      - **政治新闻**: 选举、政府政策（非行业相关）、国际关系等
      - **体育娱乐**: 体育赛事、明星八卦、电影电视等
      - **自然灾害**: 地震、洪水、火灾等灾难事件
      - **社会事件**: 抗议、示威、社会冲突等
      - **科技数码**: 手机、电脑、互联网等非健康科技
      - **金融股市**: 股票、投资、金融市场（非行业相关）
      - **交通事故**: 车祸、飞机失事等交通事件

      **重要提醒**:
      1. 仅仅提及"健康"、"安全"、"人体"等词汇不等于与我们的植物提取物业务相关
      2. 暴力犯罪中的受害者健康状况与保健品行业完全无关，必须给1-2分
      3. 如有任何疑虑，倾向于给出更低的分数

      请严格按照以下JSON格式提供你的评估报告：

      {{
        "is_relevant": true/false,
        "relevance_score": 1-10,
      }}

      仅返回JSON，不要有其他文字。
    
    optimization_prompt: |
      角色与目标 (Role & Goal)
      你是一个全自动化的新闻处理引擎。你的唯一目标是接收一份原始数据，通过严格的工作流进行处理，并最终输出一个标准化的JSON对象，不要展示推理过程。整个过程无需人工干预。

      1. 输入处理与异常 (Input & Exception Handling)
      你将收到一份"待处理数据包"。首先，你必须对数据包进行分析和甄别。

      1.1. 格式识别:
      结构化数据: 如果数据包含明确的键（如title, content, original_link），则直接将其对应到内部变量。
      非结构化数据:
      如果数据仅为一个URL，则将其识别为 original_link。
      如果数据为一段纯文本，则将其识别为 content。
      如果数据是URL和文本的混合体，运用你的逻辑能力进行拆分和识别。

      1.2. 异常处理 (Discard Rule):
      识别条件: 如果"待处理数据包"满足以下任一条件，则判定为无效数据：
      数据完全为空 (null, "")。
      数据内容明确为爬虫抓取失败的标志，例如（不限于）："404 Not Found", "Error 403 Forbidden", "Access Denied", "找不到服务器", "请求超时"。
      处理方式: 一旦判定为无效数据，必须立即中止所有后续步骤，并丢弃该数据包。不得输出任何内容。

      2. 核心工作流 (Core Workflow)
      如果数据有效，则严格按照以下流程处理：

      2.1. 步骤一：链接溯源与验证 (Link Sourcing & Verification)
      验证: 检查是否存在一个有效、可访问的 original_link。
      溯源: 如果 original_link 缺失或无效，必须利用 title 和/或 content 的核心关键词，通过互联网搜索引擎，定位到最权威、最原始的新闻来源链接。此链接将成为本任务唯一的 original_link。这是所有后续步骤的基础。

      2.2. 步骤二：内容获取与清洗 (Content Fetching & Cleaning)
      获取: 访问已验证的 original_link，获取最完整、最原始的官方新闻全文。
      清洗: 以官方新闻为准，彻底清除所有无关元素，包括但不限于：广告、相关文章推荐、版权声明、作者介绍、社交媒体分享按钮、评论区、cookie横幅、订阅请求等。确保留下的内容是纯粹的新闻主体。

      2.3. 步骤三：内容优化与强制扩充 (Content Enhancement & Mandatory Expansion)
      润色: 对清洗后的内容进行专业级润色，修正语法错误和错别字，使行文流畅、专业，符合标准新闻文体。
      硬性指标检查: 计算当前 content 的总字符数。
      强制扩充指令:
      如果字符数 少于500个字符，你 必须无条件 启动内容扩充流程。这是一条 强制命令。
      扩充方法: 必须基于 original_link 的原始报道，系统性地补充和丰富新闻内容，确保其包含完整的核心要素，如：
      事件背景: 提供更深入的上下文信息。
      关键细节: 补充具体数据、时间线、关键人物的直接引语等。
      前因后果: 解释事件的起因及其可能产生的长短期影响。
      多方视角: 引入不同利益相关方的观点或反应（若原文提及）。
      循环验证: 扩充后，必须重新计算字符数。如果仍未达到500字符，则继续返回上一步进行补充，直至达标为止。

      2.4. 步骤四：标题生成与翻译 (Title Generation & Translation)
      标题定稿: 如果原始 title 缺失或质量不高，需根据最终完善的 content，生成一个高度概括、精准、且吸引人的中文新闻标题。
      翻译: 将最终定稿的 title 和 content 精确、流畅地翻译为 简体中文。

      3. 输出格式 (Output Formatting)
      处理完成后，必须将结果封装在以下指定的JSON结构中。
      最终输出必须且只能是这个JSON对象。
      ABSOLUTELY NO additional text, explanation, comments, or any non-JSON characters before or after the JSON block. (例如，不允许出现 "这是您要的JSON：" 这样的文字)。

      {{
        "message_type": "text",
        "title": "此处为翻译成中文的标题",
        "content": "此处为经过清洗、润色、强制扩充并翻译成中文的新闻内容",
        "original_link": "此处为核实后的原始新闻链接"
      }}

      待处理数据包:
      **原始内容**: {raw_content}
      **原始链接**: {original_link}

# ========================================
# 性能和并发配置
# ========================================
# RSS异步处理配置
rss_async_enabled: true
rss_concurrency: 10
rss_rate_limit: 5

# 请求超时和重试配置
request_timeout: 30
retry_attempts: 3
retry_delay: 5

# ========================================
# AI模型配置
# ========================================
# Gemini模型设置
gemini_model: "gemini-2.5-flash"
gemini_enable_search: true

# OpenRouter模型设置
openrouter_model: "deepseek/deepseek-chat-v3-0324:free"
openrouter_enable_search: false
openrouter_endpoint: "https://openrouter.ai/api/v1"

# ========================================
# 内容抓取配置
# ========================================
content_extraction:
  enabled: true
  # API密钥在.env文件中配置

# ========================================
# 新闻源配置
# ========================================
news_sources:
  # MediaStack商业新闻API（可选）
  mediastack:
    enabled: true
    category: "health"
    limit: 100
    retry_count: 3
    # API密钥在.env文件中配置
  
  # NewsAPI商业新闻API（可选）
  newsapi:
    enabled: true
    category: "health"
    limit: 100
    retry_count: 3
    # API密钥在.env文件中配置

# ========================================
# RSS新闻源配置
# ========================================
rss_feeds_file: "rss_feeds.yaml"
rss_settings:
  # 每个RSS源最大文章数
  max_articles_per_feed: 100
  # 重试次数
  retry_count: 3
  # 请求超时时间（秒）
  timeout: 45
  # 用户代理字符串
  user_agent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
