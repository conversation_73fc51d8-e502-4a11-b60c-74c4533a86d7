# ========================================
# DailyInfo Environment Configuration Template
# ========================================
# Copy this file to .env and fill in your actual values
# DO NOT commit the .env file to version control

# ========================================
# AI API Keys (Required)
# ========================================
# Gemini API Keys - Get from https://makersuite.google.com/app/apikey
# Multiple keys can be provided (comma-separated) for load balancing
GEMINI_API_KEYS=your_gemini_api_key_1,your_gemini_api_key_2,your_gemini_api_key_3

# OpenRouter API Keys - Get from https://openrouter.ai/keys
# Multiple keys can be provided (comma-separated) for load balancing
OPENROUTER_API_KEYS=your_openrouter_api_key_1,your_openrouter_api_key_2,your_openrouter_api_key_3

# ========================================
# Feishu/Lark Integration (Required)
# ========================================
# Feishu Webhook URL - Get from your Feishu bot configuration
# Format: https://open.feishu.cn/open-apis/bot/v2/hook/your_webhook_token
WEBHOOK_URL=https://open.feishu.cn/open-apis/bot/v2/hook/your_webhook_token

# ========================================
# Commercial News APIs (Optional)
# ========================================
# MediaStack API Key - Get from https://mediastack.com/
# Provides global news coverage with health category support
MEDIASTACK_API_KEY=your_mediastack_api_key

# NewsAPI Key - Get from https://newsapi.org/
# Provides English headline news with category filtering
NEWSAPI_API_KEY=your_newsapi_api_key

# ========================================
# Content Extraction APIs (Optional)
# ========================================
# FireCrawl API Key - Get from https://firecrawl.dev/
# Used for enhanced web content extraction
FIRECRAWL_API_KEY=your_firecrawl_api_key

# Zyte API Key - Get from https://www.zyte.com/
# Alternative content extraction service
ZYTE_API_KEY=your_zyte_api_key

# ========================================
# Configuration Notes
# ========================================
# 1. At minimum, you need:
#    - At least one AI API key (GEMINI_API_KEYS or OPENROUTER_API_KEYS)
#    - WEBHOOK_URL for Feishu integration
#
# 2. Multiple API keys:
#    - Separate multiple keys with commas (no spaces)
#    - System will automatically rotate between keys for load balancing
#    - Example: key1,key2,key3
#
# 3. Optional services:
#    - MediaStack and NewsAPI provide additional news sources
#    - FireCrawl and Zyte improve content extraction quality
#    - System works without these but with reduced functionality
#
# 4. Security:
#    - Never commit the .env file to version control
#    - Keep your API keys secure and rotate them regularly
#    - Use environment-specific keys for different deployments
#
# 5. Testing:
#    - You can test with free tier API keys initially
#    - Monitor usage to avoid hitting rate limits
#    - Consider upgrading to paid tiers for production use

# ========================================
# Example Values (DO NOT USE IN PRODUCTION)
# ========================================
# GEMINI_API_KEYS=AIzaSyExample1,AIzaSyExample2
# OPENROUTER_API_KEYS=sk-or-example1,sk-or-example2
# WEBHOOK_URL=https://open.feishu.cn/open-apis/bot/v2/hook/example-token
# MEDIASTACK_API_KEY=example123456789
# NEWSAPI_API_KEY=example987654321
# FIRECRAWL_API_KEY=fc-example123
# ZYTE_API_KEY=zyte-example456
