#!/usr/bin/env python3
"""
动态设置cron环境变量脚本
确保cron任务能够访问所有必要的环境变量
"""

import os
import sys
import subprocess


def setup_cron_environment():
    """设置cron环境变量"""
    try:
        cron_file = "/etc/cron.d/dailyinfo-cron"
        
        print(f"[INFO] 开始设置cron环境变量...")
        
        # 需要传递给cron的环境变量
        env_vars = [
            'GEMINI_API_KEYS',
            'OPENROUTER_API_KEYS',
            'FIRECRAWL_API_KEY',
            'ZYTE_API_KEY',
            'MEDIASTACK_API_KEY',
            'NEWSAPI_API_KEY',
            'WEBHOOK_URL',
            'OLLAMA_ENDPOINT'
        ]
        
        if os.path.exists(cron_file):
            # 读取现有cron配置
            with open(cron_file, 'r') as f:
                lines = f.readlines()
            
            # 找到cron任务行的位置
            cron_task_line_index = -1
            for i, line in enumerate(lines):
                if line.strip().startswith('*') and 'cron_check.py' in line:
                    cron_task_line_index = i
                    break
            
            if cron_task_line_index == -1:
                print(f"[ERROR] 未找到cron任务行")
                return False
            
            # 移除现有的API密钥环境变量（如果存在）
            filtered_lines = []
            for line in lines:
                if not any(var in line for var in env_vars):
                    filtered_lines.append(line)
            
            # 在cron任务行之前添加环境变量
            new_lines = []
            for i, line in enumerate(filtered_lines):
                if line.strip().startswith('*') and 'cron_check.py' in line:
                    # 在这里插入环境变量
                    for var in env_vars:
                        value = os.getenv(var, '')
                        if value:
                            new_lines.append(f'{var}={value}\n')
                            print(f"[INFO] 添加环境变量: {var} (长度: {len(value)})")
                        else:
                            print(f"[WARN] 环境变量 {var} 未设置")
                
                new_lines.append(line)
            
            # 写回文件
            with open(cron_file, 'w') as f:
                f.writelines(new_lines)
            
            print(f"[INFO] ✅ Cron环境变量已更新")
            
            # 重新加载cron配置
            subprocess.run(['crontab', cron_file], check=True)
            print(f"[INFO] ✅ Cron配置已重新加载")
            
            # 验证配置
            print(f"[INFO] 验证cron配置:")
            with open(cron_file, 'r') as f:
                content = f.read()
                for var in env_vars:
                    if f'{var}=' in content:
                        print(f"[INFO] ✅ {var} 已设置")
                    else:
                        print(f"[WARN] ⚠️  {var} 未设置")
            
            return True
            
        else:
            print(f"[ERROR] Cron配置文件不存在: {cron_file}")
            return False
            
    except Exception as e:
        print(f"[ERROR] 设置cron环境变量失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("=== 动态Cron环境变量配置 ===")
    
    success = setup_cron_environment()
    
    if success:
        print(f"[INFO] 🎉 Cron环境变量配置完成")
        return 0
    else:
        print("[ERROR] ❌ Cron环境变量配置失败")
        return 1


if __name__ == "__main__":
    sys.exit(main())
