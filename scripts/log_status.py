#!/usr/bin/env python3
"""
日志状态监控脚本
用于检查和显示日志文件的状态信息
"""

import os
import sys
import json
from datetime import datetime
sys.path.append('/app')
from utils.log_manager import LogManager


def check_docker_logs():
    """检查Docker日志配置和状态"""
    print("=== Docker日志配置检查 ===")
    
    # 检查Docker日志驱动配置
    try:
        # 这里只能显示理论配置，实际配置需要通过docker inspect查看
        print("配置的日志策略:")
        print("  - 驱动: json-file")
        print("  - 单文件大小: 5MB")
        print("  - 保留文件数: 5个")
        print("  - 压缩: 启用")
        print("  - 总容量限制: ~25MB")
        print()
        
        print("查看实际Docker日志:")
        print("  docker-compose logs --tail=100 dailyinfo")
        print("  docker-compose logs --since=1h dailyinfo")
        print()
        
    except Exception as e:
        print(f"检查Docker日志配置失败: {e}")


def check_application_logs():
    """检查应用程序日志"""
    print("=== 应用程序日志检查 ===")
    
    log_manager = LogManager()
    stats = log_manager.get_log_statistics()
    
    print(f"日志文件统计:")
    print(f"  - 文件数量: {stats['total_files']}")
    print(f"  - 总大小: {stats['total_size_mb']} MB")
    print()
    
    # 检查具体日志文件
    log_files = [
        "/app/logs/cron.log",
        "/app/logs/cron_check.log",
    ]
    
    print("主要日志文件状态:")
    for log_file in log_files:
        if os.path.exists(log_file):
            try:
                size_mb = os.path.getsize(log_file) / (1024 * 1024)
                mtime = datetime.fromtimestamp(os.path.getmtime(log_file))
                print(f"  - {log_file}: {size_mb:.2f} MB, 修改时间: {mtime}")
            except Exception as e:
                print(f"  - {log_file}: 读取失败 ({e})")
        else:
            print(f"  - {log_file}: 不存在")
    print()


def show_log_recommendations():
    """显示日志管理建议"""
    print("=== 日志管理建议 ===")
    
    log_manager = LogManager()
    stats = log_manager.get_log_statistics()
    
    recommendations = []
    
    # 检查总大小
    if stats['total_size_mb'] > 100:
        recommendations.append("应用程序日志总大小超过100MB，建议清理")
    
    # 检查文件数量
    if stats['total_files'] > 20:
        recommendations.append("日志文件数量较多，建议清理旧文件")
    
    # 检查单个文件大小
    large_files = []
    if os.path.exists("/app/logs"):
        for file in os.listdir("/app/logs"):
            file_path = os.path.join("/app/logs", file)
            if os.path.isfile(file_path):
                try:
                    size_mb = os.path.getsize(file_path) / (1024 * 1024)
                    if size_mb > 50:
                        large_files.append(f"{file} ({size_mb:.1f}MB)")
                except Exception:
                    pass
    
    if large_files:
        recommendations.append(f"发现大文件: {', '.join(large_files)}")
    
    if recommendations:
        print("发现的问题:")
        for i, rec in enumerate(recommendations, 1):
            print(f"  {i}. {rec}")
        print()
        print("建议操作:")
        print("  - 手动清理: docker-compose exec dailyinfo python3 utils/log_manager.py")
        print("  - 查看大文件: docker-compose exec dailyinfo ls -lh /app/logs/")
    else:
        print("✅ 日志状态良好，无需特殊处理")
    
    print()


def show_log_commands():
    """显示常用的日志管理命令"""
    print("=== 常用日志管理命令 ===")
    
    commands = [
        ("查看实时日志", "docker-compose logs -f dailyinfo"),
        ("查看最近100行", "docker-compose logs --tail=100 dailyinfo"),
        ("查看最近1小时", "docker-compose logs --since=1h dailyinfo"),
        ("查看错误日志", "docker-compose logs dailyinfo | grep ERROR"),
        ("手动清理日志", "docker-compose exec dailyinfo python3 utils/log_manager.py"),
        ("查看日志文件", "docker-compose exec dailyinfo ls -lh /app/logs/"),
        ("查看磁盘使用", "docker-compose exec dailyinfo df -h /app/logs/"),
        ("重启容器清理", "docker-compose restart dailyinfo"),
    ]
    
    for desc, cmd in commands:
        print(f"  {desc}:")
        print(f"    {cmd}")
        print()


def main():
    """主函数"""
    print("DailyInfo 日志状态监控")
    print("=" * 50)
    print()
    
    check_docker_logs()
    check_application_logs()
    show_log_recommendations()
    show_log_commands()


if __name__ == "__main__":
    main()
