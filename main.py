#!/usr/bin/env python3
"""
DailyInfo 智能新闻推送系统
主程序入口
"""

import sys

# 确保实时输出（特别是在cron环境下）
sys.stdout.reconfigure(line_buffering=True)
sys.stderr.reconfigure(line_buffering=True)

from utils.config_manager import ConfigManager
from utils.workflow_manager import WorkflowManager


def print_flush(message):
    """打印消息并强制刷新输出缓冲区"""
    print(message)
    sys.stdout.flush()


def execute_task(workflow_manager: WorkflowManager):
    """执行新闻处理任务"""
    try:
        result = workflow_manager.execute_daily_task()
        
        print_flush(f"\n[INFO] ========== 任务执行结果 ==========")
        print_flush(f"[INFO] 收集新闻: {result.get('collected_count', 0)} 条")
        print_flush(f"[INFO] 处理新闻: {result.get('processed_count', 0)} 条")
        print_flush(f"[INFO] 推送新闻: {result.get('sent_count', 0)} 条")
        print_flush(f"[INFO] 执行状态: {result.get('status', 'Unknown')}")

        if result.get('error'):
            print_flush(f"[ERROR] 错误信息: {result.get('error')}")
        
        return result
        
    except Exception as e:
        print_flush(f"[ERROR] 任务执行异常: {str(e)}")
        return {
            'collected_count': 0,
            'processed_count': 0,
            'sent_count': 0,
            'status': 'Failed',
            'error': str(e)
        }


# Note: Daemon service functions removed in Docker-optimized version
# Scheduled execution is now handled by cron within Docker container


# Removed: start_daemon_service_with_immediate_run function

# Removed: stop_daemon_service function

# Removed: show_daemon_status function



# Note: Scheduled daemon functionality has been replaced by cron-based scheduling
# See cron_check.py for the current implementation


# Removed: show_status function (not needed in Docker-optimized version)


def show_help():
    """显示帮助信息"""
    print("DailyInfo - AI-Powered News Monitoring System")
    print()
    print("Usage:")
    print("  python main.py run")
    print()
    print("Description:")
    print("  This Docker-based system runs news collection and analysis tasks.")
    print("  The 'run' command executes a single news processing cycle.")
    print()
    print("Docker Deployment:")
    print("  - Scheduled execution is handled by cron within the Docker container")
    print("  - Manual execution: docker-compose exec dailyinfo python main.py run")
    print("  - View logs: docker-compose logs -f dailyinfo")
    print()
    print("Example:")
    print("  python main.py run                # Execute news processing once")


def main():
    """主函数 - Docker优化版本，仅支持run模式"""
    try:
        # 初始化配置管理器
        config_manager = ConfigManager()
        config = config_manager.load_config()

        # 初始化工作流管理器
        workflow_manager = WorkflowManager(config)

        # 解析命令行参数
        command = sys.argv[1] if len(sys.argv) > 1 else "run"

        if command == "run":
            print_flush("[INFO] Executing news processing task...")
            execute_task(workflow_manager)

        elif command == "help" or command == "--help" or command == "-h":
            show_help()

        else:
            print(f"[ERROR] Unknown command: {command}")
            print("This Docker-optimized version only supports 'run' mode.")
            print("Use 'python main.py help' for more information.")
            print()
            print("For scheduled execution, use Docker container with cron:")
            print("  docker-compose up -d")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n[INFO] 程序被用户中断")
        sys.exit(0)
        
    except Exception as e:
        print(f"[ERROR] 程序异常: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
