# Docker Health Check Documentation

## Overview

DailyInfo uses a comprehensive health check system designed specifically for daily execution patterns and timezone-aware operations. The health check accounts for the fact that news processing tasks only run once per day at the configured time.

## Health Check Components

### 1. Health Check Script (`health_check.sh`)

The health check script performs multiple checks to ensure the container is functioning correctly:

#### Critical Checks (Must Pass)
- **Cron Service**: Verifies that the cron daemon is running
- **Configuration**: Ensures config.yaml exists and is readable

#### Non-Critical Checks (Warnings Only)
- **Cron Activity**: Checks if cron tasks have executed recently (within 2 minutes)
- **Last Successful Run**: Validates the last successful task execution (within 2 days)

### 2. Health Check Configuration

```yaml
healthcheck:
  test: ["CMD-SHELL", "/app/health_check.sh"]
  interval: 2m          # Check every 2 minutes
  timeout: 30s          # Allow 30 seconds for health check
  retries: 3            # Retry 3 times before marking unhealthy
  start_period: 2m      # Wait 2 minutes before starting checks
```

## Health Check Logic

### Scoring System
- **4/4 (100%)**: All checks pass - Container is fully healthy
- **3/4 (75%)**: Critical checks pass, one non-critical fails - Container is healthy
- **2/4 (50%)**: Critical checks pass, both non-critical fail - Container is healthy
- **<2/4 (<50%)**: Critical check fails - Container is unhealthy

### Timezone Handling
- All time calculations use the container's internal timezone (Asia/Shanghai CST)
- Health check script uses `date` commands that respect the container's timezone
- No UTC/CST conversion issues

### Daily Execution Pattern
- Accounts for the fact that tasks only run once per day
- Allows up to 2 days without successful execution before marking unhealthy
- Provides informational messages about run windows

## Health Check Output

### Healthy Container Example
```
[HEALTH] 2025-07-21 07:17:43 CST Starting DailyInfo health check...
[HEALTH] 2025-07-21 07:17:43 CST ✅ Cron service is running
[HEALTH] 2025-07-21 07:17:43 CST ✅ Configuration file exists
[HEALTH] 2025-07-21 07:17:43 CST 📅 Configured run time: 06:00
[HEALTH] 2025-07-21 07:17:43 CST ✅ Cron tasks are active (log updated recently)
[HEALTH] 2025-07-21 07:17:43 CST 📅 Last successful run: 2025-07-21
[HEALTH] 2025-07-21 07:17:43 CST 📊 Days since last successful run: 0
[HEALTH] 2025-07-21 07:17:43 CST ✅ Last run is within acceptable timeframe
[HEALTH] 2025-07-21 07:17:43 CST ⏰ Currently in run window (configured: 06:00, current: 07:17)
[HEALTH] 2025-07-21 07:17:43 CST 📊 Health score: 4/4 (100%)
[HEALTH] 2025-07-21 07:17:43 CST ✅ Container is healthy
```

### Unhealthy Container Example
```
[HEALTH] 2025-07-21 07:11:16 CST Starting DailyInfo health check...
[HEALTH] 2025-07-21 07:11:16 CST ❌ Cron service is not running
[HEALTH] 2025-07-21 07:11:16 CST ❌ CRITICAL: Cron service check failed
```

## Monitoring Commands

### Check Container Health Status
```bash
# View container status
docker-compose ps

# Manual health check
docker-compose exec dailyinfo /app/health_check.sh

# View health check logs
docker inspect dailyinfo-app | jq '.[0].State.Health'
```

### Debugging Health Issues

#### 1. Cron Service Issues
```bash
# Check if cron is running
docker-compose exec dailyinfo ps aux | grep cron

# Check cron logs
docker-compose exec dailyinfo tail -f /app/logs/cron.log

# Restart container if cron is not running
docker-compose restart dailyinfo
```

#### 2. Configuration Issues
```bash
# Verify configuration file
docker-compose exec dailyinfo cat /app/config.yaml

# Test configuration loading
docker-compose exec dailyinfo python -c "from utils.config_manager import ConfigManager; print('Config OK')"
```

#### 3. Last Run Issues
```bash
# Check last run file
docker-compose exec dailyinfo cat /app/data/last_run_date

# Check if tasks are executing
docker-compose logs -f dailyinfo

# Manual task execution
docker-compose exec dailyinfo python main.py run
```

## Troubleshooting

### Common Issues

1. **Container shows as unhealthy immediately after startup**
   - This is normal during the `start_period` (2 minutes)
   - Wait for the health check to stabilize

2. **Health check fails due to missing last_run_date**
   - Normal for new deployments
   - File will be created after first successful task execution
   - Health check allows up to 2 days without successful runs

3. **Timezone-related health check failures**
   - Ensure container timezone is set to Asia/Shanghai
   - Health check uses container's internal timezone for all calculations

4. **Cron service not running**
   - Check if container started properly
   - Restart container: `docker-compose restart dailyinfo`

### Health Check Customization

You can modify the health check behavior by editing `health_check.sh`:

- `MAX_DAYS_WITHOUT_RUN`: Change acceptable days without successful execution
- Check intervals and timeouts in `docker-compose.yml`
- Add additional health checks as needed

## Best Practices

1. **Monitor health status regularly**
   ```bash
   docker-compose ps
   ```

2. **Use health check logs for debugging**
   ```bash
   docker-compose exec dailyinfo /app/health_check.sh
   ```

3. **Set up alerts for unhealthy containers**
   - Use Docker health check events
   - Monitor container status in production

4. **Regular maintenance**
   - Check logs periodically
   - Verify successful task executions
   - Monitor disk space for log files

The improved health check system provides robust monitoring while accounting for the unique characteristics of a daily news processing system.
