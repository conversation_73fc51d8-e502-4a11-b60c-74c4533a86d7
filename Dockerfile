# ========================================
# DailyInfo Docker Image
# Enterprise AI-Powered News Monitoring System
# ========================================

FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies and set timezone
RUN apt-get update && apt-get install -y \
    cron \
    tzdata \
    procps \
    && rm -rf /var/lib/apt/lists/*

# Timezone will be set dynamically from config.yaml
# Default timezone (will be overridden by setup_timezone.py)
ENV TZ=UTC

# Create user and group
RUN groupadd -r dailyinfo && useradd -r -g dailyinfo dailyinfo

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create necessary directories and make health check executable
RUN mkdir -p /app/logs /app/data && \
    chmod +x /app/health_check.sh && \
    echo "1970-01-01" > /app/data/last_run_date && \
    touch /app/logs/cron.log && \
    chown -R dailyinfo:dailyinfo /app

# Set up cron job with proper environment variables
# Note: TZ will be set dynamically by setup_timezone.py
# API keys will be added dynamically by setup_cron_env.py
RUN echo "PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin" > /etc/cron.d/dailyinfo-cron && \
    echo "TZ=UTC" >> /etc/cron.d/dailyinfo-cron && \
    echo "PYTHONUNBUFFERED=1" >> /etc/cron.d/dailyinfo-cron && \
    echo "* * * * * dailyinfo cd /app && /usr/local/bin/python3 -u cron_check.py >> /app/logs/cron.log 2>&1" >> /etc/cron.d/dailyinfo-cron && \
    echo "0 2 * * * dailyinfo cd /app && /usr/local/bin/python3 -u utils/log_manager.py >> /app/logs/cron.log 2>&1" >> /etc/cron.d/dailyinfo-cron && \
    chmod 0644 /etc/cron.d/dailyinfo-cron && \
    crontab /etc/cron.d/dailyinfo-cron

# Expose port (optional, for future web interface)
EXPOSE 8080

# Health check
HEALTHCHECK --interval=5m --timeout=10s --start-period=60s --retries=3 \
    CMD test -f /app/data/last_run_date && [ $(date +%s) - $(date -r /app/data/last_run_date +%s) -lt 90000 ] || exit 1

# Start cron service and monitor logs for Docker visibility
CMD ["sh", "-c", "echo 'Starting DailyInfo container...' && python3 setup_timezone.py && python3 setup_cron_env.py && cron && echo 'Cron service started successfully' && echo 'Monitoring application logs...' && sleep 5 && touch /app/logs/cron.log && chown dailyinfo:dailyinfo /app/logs/cron.log && tail -f /app/logs/cron.log"]
