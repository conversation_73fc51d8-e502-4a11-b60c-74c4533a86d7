#!/usr/bin/env python3
"""
动态时区配置脚本
从config.yaml读取时区配置并设置系统时区
"""

import os
import sys
import subprocess
from utils.config_manager import ConfigManager


def setup_timezone():
    """设置系统时区"""
    try:
        # 加载配置
        config_manager = ConfigManager()
        config = config_manager.load_config()
        timezone = config_manager.get_timezone()
        
        print(f"[INFO] 从配置文件读取时区: {timezone}")
        
        # 设置系统时区
        if os.path.exists(f"/usr/share/zoneinfo/{timezone}"):
            # 创建时区链接
            subprocess.run(['ln', '-snf', f'/usr/share/zoneinfo/{timezone}', '/etc/localtime'], check=True)
            
            # 写入时区文件
            with open('/etc/timezone', 'w') as f:
                f.write(timezone + '\n')
            
            # 设置环境变量
            os.environ['TZ'] = timezone
            
            print(f"[INFO] ✅ 系统时区已设置为: {timezone}")
            
            # 验证时区设置
            result = subprocess.run(['date'], capture_output=True, text=True)
            print(f"[INFO] 当前系统时间: {result.stdout.strip()}")
            
            return timezone
            
        else:
            print(f"[ERROR] ❌ 时区 {timezone} 不存在")
            return None
            
    except Exception as e:
        print(f"[ERROR] 时区设置失败: {e}")
        return None


def update_cron_timezone(timezone: str):
    """更新cron任务的时区配置"""
    try:
        cron_file = "/etc/cron.d/dailyinfo-cron"
        
        if os.path.exists(cron_file):
            # 读取现有cron配置
            with open(cron_file, 'r') as f:
                lines = f.readlines()
            
            # 更新TZ行
            updated_lines = []
            tz_found = False
            
            for line in lines:
                if line.startswith('TZ='):
                    updated_lines.append(f'TZ={timezone}\n')
                    tz_found = True
                else:
                    updated_lines.append(line)
            
            # 如果没有找到TZ行，添加一个
            if not tz_found:
                # 在PATH行后添加TZ行
                for i, line in enumerate(updated_lines):
                    if line.startswith('PATH='):
                        updated_lines.insert(i + 1, f'TZ={timezone}\n')
                        break
            
            # 写回文件
            with open(cron_file, 'w') as f:
                f.writelines(updated_lines)
            
            print(f"[INFO] ✅ Cron时区已更新为: {timezone}")
            
            # 重新加载cron配置
            subprocess.run(['crontab', cron_file], check=True)
            print(f"[INFO] ✅ Cron配置已重新加载")
            
        else:
            print(f"[WARN] Cron配置文件不存在: {cron_file}")
            
    except Exception as e:
        print(f"[ERROR] 更新cron时区失败: {e}")


def main():
    """主函数"""
    print("=== 动态时区配置 ===")
    
    # 设置系统时区
    timezone = setup_timezone()
    
    if timezone:
        # 更新cron时区
        update_cron_timezone(timezone)
        print(f"[INFO] 🎉 时区配置完成: {timezone}")
        return 0
    else:
        print("[ERROR] ❌ 时区配置失败")
        return 1


if __name__ == "__main__":
    sys.exit(main())
