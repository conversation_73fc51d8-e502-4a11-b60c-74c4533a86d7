# ========================================
# DailyInfo 系统配置文件
# ========================================
#
# 配置说明：
# - 此文件包含DailyInfo系统的所有业务配置参数
# - 敏感信息（API密钥、Webhook URL等）请在.env文件中配置
# - 修改配置后需要重启容器才能生效
#
# Configuration Notes:
# - This file contains all business configuration parameters for DailyInfo
# - Sensitive information (API keys, webhook URLs) should be in .env file
# - Container restart required after configuration changes
#
# ========================================

# ========================================
# 1. 基础运行配置 | Basic Runtime Configuration
# ========================================

# 每日执行时间（24小时制，格式：HH:MM）
# Daily execution time (24-hour format: HH:MM)
daily_run_time: "22:15"

# 时区设置 | Timezone setting
timezone: "Asia/Shanghai"

# 最大推送新闻数量 | Maximum number of news to push
max_send_limit: 10

# 时间窗口（小时）- 获取最近N小时的新闻
# Time window (hours) - fetch news from last N hours
time_window_hours: 24

# ========================================
# 2. AI服务配置 | AI Service Configuration
# ========================================

# AI服务开关配置 | AI Service Enable/Disable Switches
gemini_enabled: true          # Google Gemini AI服务
openrouter_enabled: true      # OpenRouter AI服务
ollama_enabled: true          # Ollama本地AI服务

# AI服务优先级（按顺序尝试）| AI Service Priority (try in order)
ai_preference:                    # 优先使用本地Ollama
  - gemini                    # 其次使用Gemini
  - openrouter
  - ollama                # 最后使用OpenRouter

# AI分析设置 | AI Analysis Settings
ai_settings:
  # 最低相关性评分阈值（1-10分）| Minimum relevance score threshold
  min_relevance_score: 7

# ========================================
# 3. 模块化提示词配置 | Modular Prompt Configuration
# ========================================

# 提示词设置 | Prompt Settings
prompt_settings:
  # 目标语言 | Target Language
  target_language: "简体中文"

  # 公司概况 | Company Overview
  company_overview: |
    我们是一家专业的植物提取物制造商和供应商，主要从事植物活性成分的提取、纯化和标准化生产。我们的业务模式是B2B，为全球范围内的食品、保健品和化妆品公司提供原料。

  # 产品组合 | Product Categories
  product_categories: |
    我们专注于生产和销售多种植物提取物，主要活性成分明确，应用广泛。核心产品类别和部分示例如下：

    - **抗氧化剂**: 葡萄籽提取物（原花青素、白藜芦醇）、迷迭香提取物、绿茶提取物。
    - **免疫调节剂**: 紫锥菊提取物、人参提取物、灵芝提取物、黄芪提取物。
    - **体重管理**: 柠檬马鞭草提取物、荷叶提取物、白芸豆提取物。
    - **运动营养**: 蒺藜提取物、瓜拉纳提取物、东革阿里提取物。
    - **保肝护肝**: 水飞蓟提取物（水飞蓟素）、五味子提取物。
    - **改善记忆/脑部健康**: 银杏提取物、PQQ、猴头菇提取物。
    - **男性健康**: 淫羊藿提取物、育亨宾提取物、锯叶棕榈提取物。
    - **女性健康**: 当归提取物、大豆异黄酮、黑升麻提取物。
    - **天然甜味剂**: 甜叶菊提取物、罗汉果提取物。
    - **其他热门成分**: 辅酶Q10、姜黄素、PQQ、NAD+、谷胱甘肽、褪黑素等。
    
  # 评估维度 | Evaluation Dimensions
  evaluation_dimensions: |
    1. **产品相关性**: 新闻信息是否直接或间接提到了我们正在生产或销售的产品、其活性成分或相关植物原料？是否提及了我们产品的替代品或补充品？

    2. **行业趋势与科研动态**: 新闻信息是否揭示了功能性原料、健康消费品或化妆品领域的新兴市场趋势？是否包含关于某些成分（尤其是我们产品相关的）功效、安全性的最新科学研究或临床试验结果？

    3. **国际贸易与法规政策**: 新闻信息是否涉及任何国家（特别是我们的主要市场，如北美、欧洲、东南亚等）关于食品、保健品、化妆品原料的进出口关税、贸易协定、法规标准（如FDA、EFSA）的更新或变化？是否提及任何可能影响我们供应链或国际物流的政策或事件？

    4. **市场机遇与风险**: 新闻信息是否揭示了新的市场需求或应用场景，为我们的产品提供了新的销售机会？新闻是否指出了潜在的市场风险，如某个成分的负面报道、安全警告或竞争对手的重大动向（如技术突破、大规模营销）？

  # 评分标准 | Scoring Criteria
  scoring_criteria: |
    **评分必须严格按照以下标准**：
    - **9-10分**: 直接提及我们的具体产品或活性成分，对业务有重大影响
    - **7-8分**: 涉及植物提取物、保健品、营养补充剂行业的具体趋势、法规变化、科研进展，对业务有明确参考价值
    - **5-6分**: 间接涉及功能性食品、健康产业，可能有一定参考价值
    - **3-4分**: 关联度很低，几乎无参考价值
    - **1-2分**: 完全不相关的新闻


# ========================================
# 4. 性能和并发配置 | Performance & Concurrency Configuration
# ========================================

# RSS异步处理配置 | RSS Async Processing Configuration
rss_async_enabled: true        # 启用异步处理
rss_concurrency: 10           # 并发处理数量
rss_rate_limit: 5             # 速率限制（请求/秒）

# 请求超时和重试配置 | Request Timeout & Retry Configuration
request_timeout: 30           # 请求超时时间（秒）
retry_attempts: 3             # 重试次数
retry_delay: 5                # 重试延迟（秒）

# ========================================
# 5. AI模型配置 | AI Model Configuration
# ========================================

# Gemini模型设置 | Google Gemini Model Settings
gemini_model: "gemini-2.5-flash"

# OpenRouter模型设置 | OpenRouter Model Settings
openrouter_model: "deepseek/deepseek-r1-0528:free"
openrouter_endpoint: "https://openrouter.ai/api/v1"

# Ollama本地模型设置 | Ollama Local Model Settings
# 注意：端点配置在.env文件的OLLAMA_ENDPOINT中
ollama_model: "hf.co/unsloth/Qwen3-1.7B-GGUF:Q8_0"

# ========================================
# 6. 新闻源配置 | News Source Configuration
# ========================================

# 内容抓取配置 | Content Extraction Configuration
content_extraction:
  enabled: false              # 是否启用内容抓取
  # API密钥在.env文件中配置 | API keys configured in .env file

# 商业新闻API配置 | Commercial News API Configuration
news_sources:
  # MediaStack商业新闻API | MediaStack Commercial News API
  mediastack:
    enabled: false            # 是否启用MediaStack
    category: "health"        # 新闻分类
    limit: 100               # 获取数量限制
    retry_count: 3           # 重试次数
    # API密钥在.env文件中配置 | API key in .env file

  # NewsAPI商业新闻API | NewsAPI Commercial News API
  newsapi:
    enabled: false            # 是否启用NewsAPI
    category: "health"        # 新闻分类
    limit: 100               # 获取数量限制
    retry_count: 3           # 重试次数
    # API密钥在.env文件中配置 | API key in .env file

# ========================================
# 7. RSS新闻源配置 | RSS News Feed Configuration
# ========================================

# RSS源配置文件 | RSS feeds configuration file
rss_feeds_file: "rss_feeds.yaml"

# RSS处理设置 | RSS Processing Settings
rss_settings:
  # 每个RSS源最大文章数 | Max articles per RSS feed
  max_articles_per_feed: 1

  # 重试次数 | Retry count
  retry_count: 3

  # 请求超时时间（秒）| Request timeout (seconds)
  timeout: 45

  # 用户代理字符串 | User agent string
  user_agent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"

# ========================================
# 配置文件结束 | End of Configuration File
# ========================================
