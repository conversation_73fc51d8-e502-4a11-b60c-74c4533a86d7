#!/bin/bash

# ========================================
# DailyInfo Docker Health Check Script
# ========================================
# This script provides a more robust health check for the DailyInfo container
# that accounts for the daily execution pattern and timezone handling

set -e

# Configuration
LAST_RUN_FILE="/app/data/last_run_date"
CONFIG_FILE="/app/config.yaml"
CRON_LOG="/app/logs/cron.log"
MAX_DAYS_WITHOUT_RUN=2  # Allow up to 2 days without successful execution

# Function to log health check messages
log_health() {
    echo "[HEALTH] $(date '+%Y-%m-%d %H:%M:%S %Z') $1"
}

# Function to extract configured run time from config.yaml
get_configured_run_time() {
    if [ -f "$CONFIG_FILE" ]; then
        grep "daily_run_time:" "$CONFIG_FILE" | sed 's/.*daily_run_time: *"\([^"]*\)".*/\1/' | tr -d '"'
    else
        echo "06:00"  # Default fallback
    fi
}

# Function to check if cron is running
check_cron_service() {
    # Check if cron process is running using ps (available in slim image)
    if ps aux | grep -v grep | grep -q cron; then
        log_health "✅ Cron service is running"
        return 0
    else
        log_health "❌ Cron service is not running"
        return 1
    fi
}

# Function to check if cron tasks are executing
check_cron_activity() {
    if [ -f "$CRON_LOG" ]; then
        # Check if there's been cron activity in the last 2 minutes
        local recent_activity=$(find "$CRON_LOG" -mmin -2 2>/dev/null)
        if [ -n "$recent_activity" ]; then
            log_health "✅ Cron tasks are active (log updated recently)"
            return 0
        else
            log_health "⚠️  No recent cron activity (log not updated in 2 minutes)"
            return 1
        fi
    else
        log_health "⚠️  Cron log file does not exist yet"
        return 1
    fi
}

# Function to check configuration files
check_configuration() {
    if [ -f "$CONFIG_FILE" ]; then
        log_health "✅ Configuration file exists"
        local run_time=$(get_configured_run_time)
        log_health "📅 Configured run time: $run_time"
        return 0
    else
        log_health "❌ Configuration file missing"
        return 1
    fi
}

# Function to check last successful run
check_last_run() {
    if [ ! -f "$LAST_RUN_FILE" ]; then
        log_health "⚠️  Last run file does not exist (no successful runs yet)"
        return 1
    fi
    
    local last_run_date=$(cat "$LAST_RUN_FILE" 2>/dev/null || echo "")
    if [ -z "$last_run_date" ]; then
        log_health "⚠️  Last run file is empty"
        return 1
    fi
    
    log_health "📅 Last successful run: $last_run_date"
    
    # Convert last run date to timestamp (using container's timezone)
    local last_run_timestamp=$(date -d "$last_run_date" +%s 2>/dev/null || echo "0")
    local current_timestamp=$(date +%s)
    local days_since_last_run=$(( (current_timestamp - last_run_timestamp) / 86400 ))
    
    log_health "📊 Days since last successful run: $days_since_last_run"
    
    if [ "$days_since_last_run" -le "$MAX_DAYS_WITHOUT_RUN" ]; then
        log_health "✅ Last run is within acceptable timeframe"
        return 0
    else
        log_health "❌ Too many days since last successful run ($days_since_last_run > $MAX_DAYS_WITHOUT_RUN)"
        return 1
    fi
}

# Function to check if we're in the expected run window
check_run_window() {
    local configured_time=$(get_configured_run_time)
    local current_time=$(date '+%H:%M')
    local current_hour=$(date '+%H')
    local configured_hour=$(echo "$configured_time" | cut -d: -f1)
    
    # If we're within 1 hour of the configured time, be more lenient
    local hour_diff=$(( current_hour - configured_hour ))
    if [ "$hour_diff" -lt 0 ]; then
        hour_diff=$(( hour_diff + 24 ))
    fi
    
    if [ "$hour_diff" -le 1 ]; then
        log_health "⏰ Currently in run window (configured: $configured_time, current: $current_time)"
        return 0
    else
        log_health "⏰ Outside run window (configured: $configured_time, current: $current_time)"
        return 0  # This is normal, don't fail health check
    fi
}

# Main health check logic
main() {
    log_health "Starting DailyInfo health check..."
    
    local health_score=0
    local total_checks=0
    
    # Critical checks (must pass)
    total_checks=$((total_checks + 1))
    if check_cron_service; then
        health_score=$((health_score + 1))
    else
        log_health "❌ CRITICAL: Cron service check failed"
        exit 1
    fi
    
    total_checks=$((total_checks + 1))
    if check_configuration; then
        health_score=$((health_score + 1))
    else
        log_health "❌ CRITICAL: Configuration check failed"
        exit 1
    fi
    
    # Non-critical checks (warnings only)
    total_checks=$((total_checks + 1))
    if check_cron_activity; then
        health_score=$((health_score + 1))
    fi
    
    total_checks=$((total_checks + 1))
    if check_last_run; then
        health_score=$((health_score + 1))
    fi
    
    # Always check run window (informational)
    check_run_window
    
    # Health assessment
    local health_percentage=$(( health_score * 100 / total_checks ))
    log_health "📊 Health score: $health_score/$total_checks ($health_percentage%)"
    
    if [ "$health_score" -ge 2 ]; then
        log_health "✅ Container is healthy"
        exit 0
    else
        log_health "❌ Container health check failed"
        exit 1
    fi
}

# Run the health check
main "$@"
