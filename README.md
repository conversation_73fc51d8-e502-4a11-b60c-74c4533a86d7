# DailyInfo - AI-Powered News Monitoring System

[![Docker](https://img.shields.io/badge/Docker-Supported-blue.svg)](https://www.docker.com/)
[![Python](https://img.shields.io/badge/Python-3.11+-green.svg)](https://www.python.org/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

DailyInfo is an enterprise-grade AI-powered news monitoring system that automatically collects, analyzes, and delivers relevant news content to your team via Feishu (Lark) messaging platform. The system uses advanced AI models to evaluate news relevance and filter out irrelevant content, ensuring your team receives only high-quality, industry-specific news updates.

## 🚀 Key Features

### Intelligent News Collection
- **Multi-source RSS aggregation**: Collect news from 18+ configurable RSS feeds
- **Commercial news APIs**: Integration with MediaStack and NewsAPI for comprehensive coverage
- **Async processing**: High-performance concurrent news collection
- **Smart deduplication**: Advanced algorithms to eliminate duplicate content

### AI-Powered Content Analysis
- **Relevance scoring**: AI models evaluate news relevance (1-10 scale) based on your industry focus
- **Multi-AI support**: Gemini and OpenRouter API integration with automatic failover
- **Content optimization**: AI-enhanced summaries and formatting for better readability
- **Configurable thresholds**: Customizable relevance score filtering

### Enterprise-Grade Reliability
- **Docker containerization**: Easy deployment and scaling
- **File system integrity**: Automatic configuration file validation and recovery
- **Health monitoring**: Comprehensive system health checks and reporting
- **Timezone support**: Proper handling of different time zones (Asia/Shanghai default)

### Automated Delivery
- **Scheduled execution**: Configurable daily news delivery
- **Feishu integration**: Direct message delivery to Feishu/Lark channels
- **Batch processing**: Efficient handling of multiple news items
- **Error handling**: Robust error recovery and logging

## 📋 Prerequisites

- Docker and Docker Compose
- Feishu (Lark) webhook URL
- AI API keys (Gemini and/or OpenRouter)
- Optional: MediaStack and NewsAPI keys for enhanced news coverage

## 🛠️ Installation

### 1. Clone the Repository

\`\`\`bash
git clone https://github.com/yourusername/dailyinfo.git
cd dailyinfo
\`\`\`

### 2. Configure Environment Variables

Copy the environment template and configure your API keys:

\`\`\`bash
cp .env.template .env
\`\`\`

Edit \`.env\` file with your credentials:

\`\`\`env
# AI API Keys (comma-separated for multiple keys)
GEMINI_API_KEYS=your_gemini_key1,your_gemini_key2
OPENROUTER_API_KEYS=your_openrouter_key1,your_openrouter_key2

# Feishu Webhook URL
WEBHOOK_URL=https://open.feishu.cn/open-apis/bot/v2/hook/your_webhook_token

# Optional: Commercial News APIs
MEDIASTACK_API_KEY=your_mediastack_key
NEWSAPI_API_KEY=your_newsapi_key

# Optional: Content Extraction APIs
FIRECRAWL_API_KEY=your_firecrawl_key
ZYTE_API_KEY=your_zyte_key
\`\`\`

### 3. Configure News Sources and Settings

**Option A: Use the provided example configurations**

The repository includes example configuration files that you can use directly or modify:

- `config.yaml` - Example business configuration with safe default values
- `rss_feeds.yaml` - Example RSS feed sources for various industries

**Option B: Create from templates**

Alternatively, you can create fresh configurations from templates:

\`\`\`bash
cp config.yaml.template config.yaml
cp rss_feeds.yaml.template rss_feeds.yaml
\`\`\`

**Customize your configuration:**

Edit \`config.yaml\` to customize:
- Daily execution time (\`daily_run_time\`)
- AI relevance thresholds (\`min_relevance_score\`)
- Maximum news limits (\`max_send_limit\`)
- Industry focus areas (AI evaluation prompts)

Edit \`rss_feeds.yaml\` to add your preferred news sources:
- Enable/disable specific feeds
- Add industry-specific RSS sources
- Organize feeds by categories

**Important Security Notes:**
- The provided \`config.yaml\` and \`rss_feeds.yaml\` are example files with safe values
- All sensitive information (API keys, webhook URLs) must be configured in the \`.env\` file
- Never commit real API keys or sensitive data to version control

### 4. Deploy with Docker

\`\`\`bash
# Build and start the container
docker-compose up -d

# View real-time logs (recommended for monitoring)
docker-compose logs -f dailyinfo

# Check container status
docker-compose ps

# Stop the container
docker-compose down
\`\`\`

**Docker Deployment Notes:**
- The container uses cron for automated scheduling
- All logs are visible via `docker-compose logs -f dailyinfo`
- Manual execution: `docker-compose exec dailyinfo python main.py run`
- Configuration changes are automatically detected (hot-reload)

## ⚙️ Configuration

### Main Configuration (\`config.yaml\`)

Key configuration options:

\`\`\`yaml
# Execution schedule
daily_run_time: "06:00"  # 24-hour format
timezone: "Asia/Shanghai"

# Content filtering
max_send_limit: 10  # Maximum news items per day
ai_settings:
  min_relevance_score: 8  # Minimum AI relevance score (1-10)

# RSS settings
rss_settings:
  async_enabled: true
  concurrency: 10
  rate_limit: 5  # requests per second
  max_articles_per_feed: 100
\`\`\`

### RSS Feeds Configuration (\`rss_feeds.yaml\`)

Add your news sources:

\`\`\`yaml
rss_feeds:
  - name: "TechCrunch"
    url: "https://techcrunch.com/feed/"
    category: "Technology"
    enabled: true
  
  - name: "Reuters Business"
    url: "https://feeds.reuters.com/reuters/businessNews"
    category: "Business"
    enabled: true
\`\`\`

## 🔧 Usage

### Manual Execution

Execute news processing manually:

\`\`\`bash
# Run news collection and processing once
docker-compose exec dailyinfo python main.py run

# View help information
docker-compose exec dailyinfo python main.py help
\`\`\`

### Scheduled Execution

The system automatically runs daily at the configured time (default: 06:00 Asia/Shanghai).

**Monitor real-time activity:**

\`\`\`bash
# View live Docker logs (recommended for monitoring)
docker-compose logs -f dailyinfo

# View specific log files inside container
docker-compose exec dailyinfo tail -f /app/logs/cron.log
\`\`\`

**Example Docker log output:**
\`\`\`
dailyinfo-app  | Starting DailyInfo container...
dailyinfo-app  | Cron service started successfully
dailyinfo-app  | Monitoring application logs...
dailyinfo-app  | [2025-07-21 06:00:01] [INFO] 时间匹配！开始执行任务...
dailyinfo-app  | [INFO] Executing news processing task...
dailyinfo-app  | [INFO] 收集新闻: 25 条
dailyinfo-app  | [INFO] 处理新闻: 8 条
dailyinfo-app  | [INFO] 推送新闻: 8 条
\`\`\`

### System Status

Check container and service status:

\`\`\`bash
# View container status
docker-compose ps

# Check system health
docker-compose exec dailyinfo python utils/health_monitor.py
\`\`\`

## 🔍 Troubleshooting

### Common Issues

1. **Container fails to start**
   \`\`\`bash
   # Check logs
   docker-compose logs dailyinfo
   
   # Verify configuration
   docker-compose exec dailyinfo python -c "from utils.config_manager import ConfigManager; print('Config OK')"
   \`\`\`

2. **No news delivered**
   - Check AI API keys are valid
   - Verify relevance score threshold isn't too high
   - Review RSS feed URLs are accessible

3. **Time zone issues**
   - Ensure \`TZ=Asia/Shanghai\` in Docker environment
   - Check \`daily_run_time\` format is correct (HH:MM)

### Log Files

- Application logs: \`docker-compose logs dailyinfo\`
- Cron logs: \`/app/logs/cron.log\`
- Health reports: \`/app/data/health_reports/\`

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (\`git checkout -b feature/amazing-feature\`)
3. Commit your changes (\`git commit -m 'feat: add amazing feature'\`)
4. Push to the branch (\`git push origin feature/amazing-feature\`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Built with Python 3.11+ and Docker
- AI integration via Gemini and OpenRouter APIs
- News delivery via Feishu (Lark) platform
- RSS parsing with feedparser library

## 📞 Support

For support and questions:
- Create an issue on GitHub
- Check the troubleshooting section
- Review the configuration documentation

---

**Note**: This system is designed for enterprise use. Ensure you comply with all relevant news source terms of service and API usage policies.
