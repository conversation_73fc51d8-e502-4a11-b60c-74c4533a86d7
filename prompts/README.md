# AI提示词模板系统

## 概述

本目录包含DailyInfo系统的AI提示词模板文件，实现了提示词的模块化管理。

## 文件结构

```
prompts/
├── README.md                    # 本文档
├── evaluation_template.txt      # 新闻评估提示词模板
├── optimization_template.txt    # 内容优化提示词模板
└── template_config.yaml        # 模板配置参数（可选）
```

## 模板系统特性

### 1. 参数化模板
- 使用 `{parameter_name}` 格式的占位符
- 支持从config.yaml动态读取配置参数
- 模板与配置分离，便于维护

### 2. 配置参数分类

#### 业务配置参数（在config.yaml中定义）
- `company_overview`: 公司概况（包含核心优势信息）
- `product_categories`: 产品组合
- `evaluation_dimensions`: 评估维度
- `scoring_criteria`: 评分标准
- `target_language`: 目标语言

#### 动态参数（运行时传入）
- `title`: 新闻标题
- `content`: 新闻内容
- `link`: 新闻链接
- `pubDate`: 发布时间
- `raw_content`: 原始内容
- `original_link`: 原始链接

## 使用方式

### 1. 模板加载
```python
from utils.prompt_manager import PromptManager

prompt_manager = PromptManager(config)
evaluation_prompt = prompt_manager.get_evaluation_prompt()
optimization_prompt = prompt_manager.get_optimization_prompt()
```

### 2. 参数替换
```python
# 评估提示词
final_prompt = evaluation_prompt.format(
    title=news['title'],
    content=news['content'],
    link=news['link'],
    pubDate=news['pubDate']
)

# 优化提示词
final_prompt = optimization_prompt.format(
    raw_content=raw_content,
    original_link=original_link
)
```

## 配置示例

在config.yaml中添加提示词配置：

```yaml
prompt_settings:
  target_language: "简体中文"

  company_overview: |
    我们是一家专业的植物提取物制造商和供应商...
    **核心优势**: 强大的研发能力、严格的质量控制...

  product_categories: |
    - **抗氧化剂**: 葡萄籽提取物...
    - **免疫调节剂**: 紫锥菊提取物...

  evaluation_dimensions: |
    1. **产品相关性**: 新闻信息是否直接或间接提到...
    2. **行业趋势与科研动态**: 新闻信息是否揭示...

  scoring_criteria: |
    - **9-10分**: 直接提及我们的具体产品...
    - **7-8分**: 涉及植物提取物、保健品...
```

## 向后兼容性

- 如果prompts目录不存在，系统将回退到config.yaml中的原始配置
- 现有的evaluation_prompt和optimization_prompt配置仍然有效
- 新旧配置可以并存，新配置优先级更高

## 扩展性

### 添加新模板
1. 在prompts目录创建新的模板文件
2. 在config.yaml中添加相应的配置参数
3. 在PromptManager中添加对应的加载方法

### 多语言支持
- 可以创建不同语言的模板文件
- 通过target_language参数选择对应模板
- 例如：evaluation_template_en.txt, evaluation_template_zh.txt

### 行业定制
- 不同行业可以使用不同的模板文件
- 通过配置参数切换行业模板
- 便于系统在不同业务场景下的复用
