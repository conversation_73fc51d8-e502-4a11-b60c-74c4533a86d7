#!/usr/bin/env python3
"""
配置管理器 - 统一管理配置文件加载和环境变量
"""

import yaml
import os
import shutil
import logging
from typing import Dict, Any
from datetime import datetime
from dotenv import load_dotenv


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: str = None):
        """初始化配置管理器"""
        if config_path is None:
            script_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            config_path = os.path.join(script_dir, 'config.yaml')

        self.config_path = config_path
        self.config = None
        self.logger = self._setup_logger()

        # 加载.env文件
        self._load_env_file()

    def _load_env_file(self):
        """加载.env文件"""
        try:
            script_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            env_path = os.path.join(script_dir, '.env')

            if os.path.exists(env_path):
                load_dotenv(env_path)
                print(f"[INFO] 成功加载环境变量文件: {env_path}")
            else:
                print(f"[WARN] .env文件不存在: {env_path}")
        except Exception as e:
            print(f"[ERROR] 加载.env文件失败: {str(e)}")

    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('ConfigManager')
        logger.setLevel(logging.INFO)

        # 避免重复添加处理器
        if not logger.handlers:
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)

        return logger

    def _verify_config_file_integrity(self) -> bool:
        """验证配置文件完整性"""
        try:
            # 检查文件是否存在
            if not os.path.exists(self.config_path):
                self.logger.error(f"配置文件不存在: {self.config_path}")
                return self._attempt_auto_repair()

            # 检查是否是文件夹
            if os.path.isdir(self.config_path):
                self.logger.error(f"配置文件路径是文件夹而非文件: {self.config_path}")
                return self._attempt_auto_repair()

            # 检查是否是常规文件
            if not os.path.isfile(self.config_path):
                self.logger.error(f"配置文件路径不是常规文件: {self.config_path}")
                return self._attempt_auto_repair()

            # 检查文件是否可读
            try:
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if not content.strip():
                        self.logger.warning(f"配置文件为空: {self.config_path}")
                        return self._attempt_auto_repair()
            except Exception as e:
                self.logger.error(f"配置文件不可读: {self.config_path}, 错误: {e}")
                return self._attempt_auto_repair()

            # 验证YAML格式
            try:
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    yaml.safe_load(f)
            except yaml.YAMLError as e:
                self.logger.error(f"配置文件YAML格式错误: {self.config_path}, 错误: {e}")
                return self._attempt_auto_repair()

            return True

        except Exception as e:
            self.logger.error(f"配置文件完整性验证失败: {e}")
            return False

    def _attempt_auto_repair(self) -> bool:
        """尝试自动修复配置文件"""
        template_path = self.config_path + '.template'

        if not os.path.exists(template_path):
            self.logger.error(f"模板文件不存在，无法自动修复: {template_path}")
            return False

        try:
            # 备份现有文件（如果存在且不是文件夹）
            if os.path.exists(self.config_path) and not os.path.isdir(self.config_path):
                backup_path = f"{self.config_path}.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                shutil.move(self.config_path, backup_path)
                self.logger.info(f"已备份现有配置文件: {backup_path}")

            # 删除文件夹（如果存在）
            if os.path.isdir(self.config_path):
                shutil.rmtree(self.config_path)
                self.logger.info(f"已删除错误的文件夹: {self.config_path}")

            # 从模板复制
            shutil.copy2(template_path, self.config_path)
            self.logger.info(f"已从模板恢复配置文件: {self.config_path}")

            # 验证修复结果
            return self._verify_config_file_integrity()

        except Exception as e:
            self.logger.error(f"自动修复配置文件失败: {e}")
            return False

    def load_config(self) -> Dict[str, Any]:
        """加载配置文件，支持环境变量覆盖"""
        try:
            # 首先验证配置文件完整性
            if not self._verify_config_file_integrity():
                raise Exception("配置文件完整性验证失败")

            # 加载YAML配置文件
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self.config = yaml.safe_load(f)
            
            # 使用环境变量覆盖敏感配置
            self._apply_environment_overrides()
            
            # 验证配置
            self._validate_config()

            return self.config
            
        except Exception as e:
            print(f"[ERROR] 加载配置文件失败: {str(e)}")
            raise
    
    def _apply_environment_overrides(self):
        """应用环境变量覆盖 - 仅处理敏感信息"""
        # 敏感信息的环境变量映射
        sensitive_env_mappings = {
            'gemini_api_keys': 'GEMINI_API_KEYS',
            'openrouter_api_keys': 'OPENROUTER_API_KEYS',
            'firecrawl_api_key': 'FIRECRAWL_API_KEY',
            'zyte_api_key': 'ZYTE_API_KEY',
            'mediastack_api_key': 'MEDIASTACK_API_KEY',
            'newsapi_api_key': 'NEWSAPI_API_KEY',
            'webhook_url': 'WEBHOOK_URL'
        }

        # 处理敏感信息的环境变量覆盖
        for config_key, env_key in sensitive_env_mappings.items():
            env_value = os.getenv(env_key)
            if env_value:
                if config_key.endswith('_api_keys') and ',' in env_value:
                    # 处理多个API密钥（逗号分隔）
                    self.config[config_key] = [key.strip() for key in env_value.split(',')]
                else:
                    # 处理单个值
                    self.config[config_key] = env_value
                print(f"[INFO] 使用环境变量 {env_key} 覆盖配置")

        # 处理嵌套的API密钥配置
        self._apply_nested_api_keys()

    def _apply_nested_api_keys(self):
        """处理嵌套结构中的API密钥配置"""
        # 处理news_sources中的API密钥
        news_sources = self.config.get('news_sources', {})

        # MediaStack API密钥
        if 'mediastack_api_key' in self.config and 'mediastack' in news_sources:
            news_sources['mediastack']['api_key'] = self.config['mediastack_api_key']

        # NewsAPI密钥
        if 'newsapi_api_key' in self.config and 'newsapi' in news_sources:
            news_sources['newsapi']['api_key'] = self.config['newsapi_api_key']

        # 处理content_extraction中的API密钥
        content_extraction = self.config.get('content_extraction', {})
        if 'firecrawl_api_key' in self.config:
            content_extraction['firecrawl_api_key'] = self.config['firecrawl_api_key']
        if 'zyte_api_key' in self.config:
            content_extraction['zyte_api_key'] = self.config['zyte_api_key']

    def _validate_config(self):
        """验证配置完整性"""
        required_keys = [
            'daily_run_time', 'timezone', 'max_send_limit'
        ]
        
        missing_keys = []
        for key in required_keys:
            if key not in self.config:
                missing_keys.append(key)
        
        if missing_keys:
            raise ValueError(f"配置文件缺少必需的键: {missing_keys}")
        
        # 验证AI设置
        if 'ai_settings' not in self.config:
            raise ValueError("配置文件缺少ai_settings部分")
        
        ai_settings = self.config['ai_settings']
        if 'min_relevance_score' not in ai_settings:
            raise ValueError("ai_settings缺少min_relevance_score")
        
        # 验证新闻源设置
        news_sources_config = self.config.get('news_sources', {})
        news_sources = [
            self.config.get('rss_feeds'),
            news_sources_config.get('mediastack', {}).get('api_key'),
            news_sources_config.get('newsapi', {}).get('api_key')
        ]
        
        if not any(news_sources):
            print("[WARN] 没有配置任何新闻源")
    
    def get_news_source_config(self) -> Dict[str, Any]:
        """获取新闻源配置"""
        return {
            'rss_feeds': self.config.get('rss_feeds', []),
            'rss_settings': self.config.get('rss_settings', {}),
            'news_sources': self.config.get('news_sources', {})
        }
    
    def get_ai_config(self) -> Dict[str, Any]:
        """获取AI配置"""
        return {
            'gemini_api_keys': self.config.get('gemini_api_keys', []),
            'gemini_model': self.config.get('gemini_model'),
            'openrouter_api_keys': self.config.get('openrouter_api_keys', []),
            'openrouter_model': self.config.get('openrouter_model'),
            'ai_settings': self.config.get('ai_settings', {}),
            'firecrawl_api_key': self.config.get('firecrawl_api_key')
        }
    
    def get_publisher_config(self) -> Dict[str, Any]:
        """获取发布配置"""
        return {
            'webhook_url': self.config.get('webhook_url'),
            'max_send_limit': self.config.get('max_send_limit', 10),
            'daily_run_time': self.config.get('daily_run_time', '06:00'),
            'timezone': self.config.get('timezone', 'Asia/Shanghai')
        }

    def get_timezone(self) -> str:
        """获取配置的时区"""
        return self.config.get('timezone', 'Asia/Shanghai')

    def get_full_config(self) -> Dict[str, Any]:
        """获取完整配置"""
        if self.config is None:
            raise ValueError("配置尚未加载，请先调用load_config()")
        return self.config.copy()
    
    def update_config(self, updates: Dict[str, Any]):
        """更新配置"""
        if self.config is None:
            raise ValueError("配置尚未加载，请先调用load_config()")
        
        self.config.update(updates)
        print(f"[INFO] 配置已更新: {list(updates.keys())}")
    
    def save_config(self, backup: bool = True):
        """保存配置到文件"""
        if self.config is None:
            raise ValueError("没有配置可保存")
        
        try:
            # 创建备份
            if backup and os.path.exists(self.config_path):
                backup_path = f"{self.config_path}.backup"
                import shutil
                shutil.copy2(self.config_path, backup_path)
                print(f"[INFO] 配置备份已创建: {backup_path}")
            
            # 保存配置
            with open(self.config_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
            
            print(f"[INFO] 配置已保存到: {self.config_path}")
            
        except Exception as e:
            print(f"[ERROR] 保存配置失败: {str(e)}")
            raise


if __name__ == "__main__":
    # 测试代码
    print("🧪 测试配置管理器...")
    
    try:
        config_manager = ConfigManager()
        config = config_manager.load_config()
        
        print("✅ 配置加载成功")
        print(f"📊 配置项数量: {len(config)}")
        
        # 测试分类配置获取
        news_config = config_manager.get_news_source_config()
        ai_config = config_manager.get_ai_config()
        publisher_config = config_manager.get_publisher_config()
        
        print(f"📰 新闻源配置: {len(news_config)} 项")
        print(f"🤖 AI配置: {len(ai_config)} 项")
        print(f"📤 发布配置: {len(publisher_config)} 项")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
