#!/usr/bin/env python3
"""
提示词管理器 - 负责加载和管理AI提示词模板
支持模块化配置和向后兼容性
"""

import os
from typing import Dict, Any, Optional


class PromptManager:
    """提示词管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化提示词管理器
        
        Args:
            config: 完整的配置字典
        """
        self.config = config
        self.prompts_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'prompts')
        
        # 获取提示词配置
        self.prompt_settings = config.get('prompt_settings', {})
        
        # 向后兼容：从ai_settings中获取原始提示词
        ai_settings = config.get('ai_settings', {})
        self.legacy_prompts = ai_settings.get('prompts', {})
        
        print(f"[INFO] 提示词管理器初始化完成")
        print(f"[INFO] 模板目录: {self.prompts_dir}")
        print(f"[INFO] 模块化配置: {'启用' if self._has_modular_config() else '禁用（使用传统配置）'}")
    
    def _has_modular_config(self) -> bool:
        """检查是否启用了模块化配置"""
        return bool(self.prompt_settings) and os.path.exists(self.prompts_dir)
    
    def _load_template_file(self, filename: str) -> Optional[str]:
        """
        加载模板文件
        
        Args:
            filename: 模板文件名
            
        Returns:
            模板内容，如果文件不存在返回None
        """
        template_path = os.path.join(self.prompts_dir, filename)
        
        if not os.path.exists(template_path):
            print(f"[WARN] 模板文件不存在: {template_path}")
            return None
            
        try:
            with open(template_path, 'r', encoding='utf-8') as f:
                content = f.read()
            print(f"[INFO] 成功加载模板文件: {filename}")
            return content
        except Exception as e:
            print(f"[ERROR] 加载模板文件失败 {filename}: {str(e)}")
            return None
    
    def _format_template_with_config(self, template: str) -> str:
        """
        使用配置参数格式化模板
        
        Args:
            template: 原始模板内容
            
        Returns:
            格式化后的模板
        """
        try:
            # 获取配置参数
            params = {
                'company_overview': self.prompt_settings.get('company_overview', ''),
                'product_categories': self.prompt_settings.get('product_categories', ''),
                'evaluation_dimensions': self.prompt_settings.get('evaluation_dimensions', ''),
                'scoring_criteria': self.prompt_settings.get('scoring_criteria', ''),
                'target_language': self.prompt_settings.get('target_language', '简体中文'),
                'news_time_filter_hours': self.prompt_settings.get('time_window_hours', 24),
            }
            
            # 格式化模板
            formatted_template = template.format(**params)
            return formatted_template
            
        except KeyError as e:
            print(f"[ERROR] 模板参数缺失: {str(e)}")
            print(f"[WARN] 返回原始模板")
            return template
        except Exception as e:
            print(f"[ERROR] 模板格式化失败: {str(e)}")
            print(f"[WARN] 返回原始模板")
            return template
    
    def get_evaluation_prompt(self) -> str:
        """
        获取评估提示词
        
        Returns:
            完整的评估提示词
        """
        # 优先使用模块化配置
        if self._has_modular_config():
            template = self._load_template_file('evaluation_template.txt')
            if template:
                return self._format_template_with_config(template)
        
        # 回退到传统配置
        legacy_prompt = self.legacy_prompts.get('evaluation_prompt', '')
        if legacy_prompt:
            print("[INFO] 使用传统配置中的evaluation_prompt")
            return legacy_prompt
        
        # 如果都没有，返回默认提示词
        print("[WARN] 未找到evaluation_prompt配置，使用默认提示词")
        return self._get_default_evaluation_prompt()
    
    def get_optimization_prompt(self) -> str:
        """
        获取优化提示词
        
        Returns:
            完整的优化提示词
        """
        # 优先使用模块化配置
        if self._has_modular_config():
            template = self._load_template_file('optimization_template.txt')
            if template:
                return self._format_template_with_config(template)
        
        # 回退到传统配置
        legacy_prompt = self.legacy_prompts.get('optimization_prompt', '')
        if legacy_prompt:
            print("[INFO] 使用传统配置中的optimization_prompt")
            return legacy_prompt
        
        # 如果都没有，返回默认提示词
        print("[WARN] 未找到optimization_prompt配置，使用默认提示词")
        return self._get_default_optimization_prompt()
    
    def _get_default_evaluation_prompt(self) -> str:
        """获取默认评估提示词"""
        return """
请评估以下新闻的相关性：

**新闻标题**: {title}
**新闻内容**: {content}
**新闻链接**: {link}

请按照以下JSON格式返回评估结果：
{{
  "is_relevant": true/false,
  "relevance_score": 1-10,
}}
"""
    
    def _get_default_optimization_prompt(self) -> str:
        """获取默认优化提示词"""
        return """
请优化以下新闻内容：

**原始内容**: {raw_content}
**原始链接**: {original_link}

请按照以下JSON格式返回优化结果：
{{
  "message_type": "text",
  "title": "优化后的标题",
  "content": "优化后的内容",
  "original_link": "原始链接"
}}
"""
    
    def validate_configuration(self) -> Dict[str, Any]:
        """
        验证配置完整性
        
        Returns:
            验证结果字典
        """
        result = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'config_type': 'legacy'
        }
        
        # 检查模块化配置
        if self._has_modular_config():
            result['config_type'] = 'modular'
            
            # 检查必需的配置参数
            required_params = [
                'company_overview',
                'product_categories',
                'evaluation_dimensions',
                'scoring_criteria'
            ]

            # 检查可选参数的默认值
            optional_params = {
                'target_language': '简体中文',
                'news_time_filter_hours': 24
            }
            
            for param in required_params:
                if not self.prompt_settings.get(param):
                    result['errors'].append(f"缺少必需的配置参数: {param}")
                    result['is_valid'] = False
            
            # 检查模板文件
            template_files = ['evaluation_template.txt', 'optimization_template.txt']
            for filename in template_files:
                if not self._load_template_file(filename):
                    result['errors'].append(f"模板文件缺失或无法读取: {filename}")
                    result['is_valid'] = False
        
        # 检查传统配置
        elif self.legacy_prompts:
            if not self.legacy_prompts.get('evaluation_prompt'):
                result['warnings'].append("传统配置中缺少evaluation_prompt")
            if not self.legacy_prompts.get('optimization_prompt'):
                result['warnings'].append("传统配置中缺少optimization_prompt")
        
        else:
            result['errors'].append("未找到任何有效的提示词配置")
            result['is_valid'] = False
        
        return result
