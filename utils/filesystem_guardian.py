#!/usr/bin/env python3
"""
文件系统守护者 - 确保关键配置文件的完整性
防止配置文件意外变成文件夹，并提供自动修复功能
"""

import os
import shutil
import logging
from typing import List, Dict, Tuple, Optional
from datetime import datetime
import yaml

class FileSystemGuardian:
    """文件系统完整性守护者"""
    
    def __init__(self, base_path: str = "."):
        self.base_path = base_path
        self.logger = self._setup_logger()
        
        # 关键配置文件定义
        self.critical_files = {
            'config.yaml': {
                'path': os.path.join(base_path, 'config.yaml'),
                'template': os.path.join(base_path, 'config.yaml.template'),
                'type': 'yaml',
                'required': True
            },
            'rss_feeds.yaml': {
                'path': os.path.join(base_path, 'rss_feeds.yaml'),
                'template': os.path.join(base_path, 'rss_feeds.yaml.template'),
                'type': 'yaml',
                'required': True
            },
            '.env': {
                'path': os.path.join(base_path, '.env'),
                'template': os.path.join(base_path, '.env.template'),
                'type': 'env',
                'required': True
            }
        }
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('FileSystemGuardian')
        logger.setLevel(logging.INFO)
        
        # 创建日志目录
        log_dir = os.path.join(self.base_path, 'logs')
        os.makedirs(log_dir, exist_ok=True)
        
        # 文件处理器
        file_handler = logging.FileHandler(
            os.path.join(log_dir, 'filesystem_guardian.log')
        )
        file_handler.setLevel(logging.INFO)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # 格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger
    
    def check_file_integrity(self, file_path: str) -> Tuple[bool, str]:
        """
        检查单个文件的完整性
        
        Returns:
            (is_valid, status_message)
        """
        if not os.path.exists(file_path):
            return False, f"文件不存在: {file_path}"
        
        if os.path.isdir(file_path):
            return False, f"路径是文件夹而非文件: {file_path}"
        
        if not os.path.isfile(file_path):
            return False, f"路径不是常规文件: {file_path}"
        
        # 检查文件是否可读
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                f.read(1)  # 尝试读取第一个字符
        except Exception as e:
            return False, f"文件不可读: {file_path}, 错误: {e}"
        
        return True, f"文件完整性正常: {file_path}"
    
    def validate_yaml_file(self, file_path: str) -> Tuple[bool, str]:
        """验证YAML文件格式"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                yaml.safe_load(f)
            return True, f"YAML格式正确: {file_path}"
        except yaml.YAMLError as e:
            return False, f"YAML格式错误: {file_path}, 错误: {e}"
        except Exception as e:
            return False, f"文件读取错误: {file_path}, 错误: {e}"
    
    def restore_from_template(self, file_name: str) -> bool:
        """从模板恢复文件"""
        file_info = self.critical_files.get(file_name)
        if not file_info:
            self.logger.error(f"未知的文件名: {file_name}")
            return False
        
        file_path = file_info['path']
        template_path = file_info['template']
        
        if not os.path.exists(template_path):
            self.logger.error(f"模板文件不存在: {template_path}")
            return False
        
        try:
            # 如果目标是文件夹，先删除
            if os.path.isdir(file_path):
                self.logger.warning(f"删除文件夹: {file_path}")
                shutil.rmtree(file_path)
            elif os.path.exists(file_path):
                self.logger.warning(f"备份现有文件: {file_path}")
                backup_path = f"{file_path}.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                shutil.move(file_path, backup_path)
            
            # 从模板复制
            shutil.copy2(template_path, file_path)
            self.logger.info(f"成功从模板恢复文件: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"从模板恢复文件失败: {file_path}, 错误: {e}")
            return False
    
    def comprehensive_check(self) -> Dict[str, any]:
        """全面的文件系统完整性检查"""
        results = {
            'timestamp': datetime.now().isoformat(),
            'overall_status': 'healthy',
            'files': {},
            'issues': [],
            'auto_repairs': []
        }
        
        self.logger.info("开始文件系统完整性检查...")
        
        for file_name, file_info in self.critical_files.items():
            file_path = file_info['path']
            file_type = file_info['type']
            
            # 基础完整性检查
            is_valid, message = self.check_file_integrity(file_path)
            
            file_result = {
                'path': file_path,
                'type': file_type,
                'exists': os.path.exists(file_path),
                'is_file': os.path.isfile(file_path) if os.path.exists(file_path) else False,
                'is_directory': os.path.isdir(file_path) if os.path.exists(file_path) else False,
                'is_valid': is_valid,
                'message': message
            }
            
            # YAML文件格式验证
            if is_valid and file_type == 'yaml':
                yaml_valid, yaml_message = self.validate_yaml_file(file_path)
                file_result['yaml_valid'] = yaml_valid
                file_result['yaml_message'] = yaml_message
                if not yaml_valid:
                    is_valid = False
                    file_result['is_valid'] = False
            
            results['files'][file_name] = file_result
            
            # 处理问题文件
            if not is_valid:
                results['overall_status'] = 'issues_detected'
                issue = f"{file_name}: {message}"
                results['issues'].append(issue)
                self.logger.warning(issue)
                
                # 自动修复
                if file_info['required']:
                    self.logger.info(f"尝试自动修复: {file_name}")
                    if self.restore_from_template(file_name):
                        repair_msg = f"成功修复: {file_name}"
                        results['auto_repairs'].append(repair_msg)
                        self.logger.info(repair_msg)
                        
                        # 重新验证
                        is_valid_after, message_after = self.check_file_integrity(file_path)
                        if is_valid_after and file_type == 'yaml':
                            yaml_valid_after, _ = self.validate_yaml_file(file_path)
                            is_valid_after = yaml_valid_after
                        
                        if is_valid_after:
                            results['files'][file_name]['repaired'] = True
                            results['files'][file_name]['is_valid'] = True
                            results['files'][file_name]['message'] = f"已修复: {message_after}"
                        else:
                            results['files'][file_name]['repair_failed'] = True
        
        # 更新整体状态
        if results['auto_repairs'] and not results['issues']:
            results['overall_status'] = 'repaired'
        elif results['auto_repairs']:
            results['overall_status'] = 'partially_repaired'
        
        self.logger.info(f"文件系统检查完成，状态: {results['overall_status']}")
        return results
    
    def create_startup_check_script(self) -> str:
        """创建启动检查脚本"""
        script_content = '''#!/bin/bash
# DailyInfo 启动前文件系统完整性检查

echo "🔍 执行启动前文件系统完整性检查..."

# 检查关键配置文件
python3 -c "
from utils.filesystem_guardian import FileSystemGuardian
import sys

guardian = FileSystemGuardian('/app')
results = guardian.comprehensive_check()

if results['overall_status'] in ['healthy', 'repaired']:
    print('✅ 文件系统完整性检查通过')
    sys.exit(0)
else:
    print('❌ 文件系统完整性检查失败')
    print('问题:', results['issues'])
    sys.exit(1)
"

if [ $? -eq 0 ]; then
    echo "✅ 启动检查通过，继续启动服务..."
else
    echo "❌ 启动检查失败，请检查配置文件"
    exit 1
fi
'''
        
        script_path = os.path.join(self.base_path, 'startup_check.sh')
        with open(script_path, 'w') as f:
            f.write(script_content)
        
        # 设置执行权限
        os.chmod(script_path, 0o755)
        
        self.logger.info(f"创建启动检查脚本: {script_path}")
        return script_path


def main():
    """主函数 - 命令行接口"""
    import sys
    
    guardian = FileSystemGuardian()
    
    if len(sys.argv) > 1 and sys.argv[1] == '--create-startup-script':
        guardian.create_startup_check_script()
        return
    
    # 执行完整性检查
    results = guardian.comprehensive_check()
    
    # 输出结果
    print(f"\n📊 检查结果: {results['overall_status']}")
    if results['issues']:
        print("❌ 发现问题:")
        for issue in results['issues']:
            print(f"  - {issue}")
    
    if results['auto_repairs']:
        print("🔧 自动修复:")
        for repair in results['auto_repairs']:
            print(f"  - {repair}")
    
    # 退出码
    if results['overall_status'] in ['healthy', 'repaired']:
        sys.exit(0)
    else:
        sys.exit(1)


if __name__ == '__main__':
    main()
