#!/usr/bin/env python3
"""
AI内容分析器 - 重构版本
"""

import json
import time
import requests
import random
from typing import Dict, Any, List, Optional
from .prompt_manager import PromptManager


class AIContentAnalyzer:
    """AI内容分析器 - 重构版本"""

    def __init__(self, config: Dict[str, Any]):
        """
        初始化AI分析器

        Args:
            config: 完整的配置字典
        """
        self.config = config

        # AI首选项配置
        self.ai_preference = config.get('ai_preference', ['openrouter', 'gemini'])

        # Gemini配置（支持多API）
        self.gemini_api_keys = config.get('gemini_api_keys', [])
        self.gemini_model = config.get('gemini_model', 'gemini-2.5-flash')
        # Gemini默认启用搜索功能
        self.gemini_enable_search = True

        # OpenRouter配置（支持多API）
        self.openrouter_api_keys = config.get('openrouter_api_keys', [])
        self.openrouter_endpoint = config.get('openrouter_endpoint', 'https://openrouter.ai/api/v1')
        self.openrouter_model = config.get('openrouter_model', 'deepseek/deepseek-r1-0528:free')
        # OpenRouter不支持搜索功能
        self.openrouter_enable_search = False

        # Ollama配置（本地AI）- 从环境变量读取端点
        import os
        self.ollama_endpoint = os.getenv('OLLAMA_ENDPOINT', 'http://localhost:11434')
        self.ollama_model = config.get('ollama_model', 'llama3.2:3b')
        self.ollama_enable_search = False  # 本地模型不支持搜索

        # AI服务开关配置
        self.gemini_enabled = config.get('gemini_enabled', True)
        self.openrouter_enabled = config.get('openrouter_enabled', True)
        self.ollama_enabled = config.get('ollama_enabled', True)

        # 初始化提示词管理器
        self.prompt_manager = PromptManager(config)

        # 获取提示词（支持模块化配置和向后兼容）
        self.evaluation_prompt = self.prompt_manager.get_evaluation_prompt()
        self.optimization_prompt = self.prompt_manager.get_optimization_prompt()

        # 验证提示词配置
        validation_result = self.prompt_manager.validate_configuration()
        if not validation_result['is_valid']:
            print(f"[WARN] 提示词配置验证失败: {validation_result['errors']}")
        if validation_result['warnings']:
            print(f"[WARN] 提示词配置警告: {validation_result['warnings']}")
        print(f"[INFO] 提示词配置类型: {validation_result['config_type']}")

        print(f"[INFO] AI首选项: {self.ai_preference}")
        print(f"[INFO] Gemini服务: {'启用' if self.gemini_enabled else '禁用'}, API数量: {len(self.gemini_api_keys)}, 联网搜索: {'启用' if self.gemini_enable_search else '禁用'}")
        print(f"[INFO] OpenRouter服务: {'启用' if self.openrouter_enabled else '禁用'}, API数量: {len(self.openrouter_api_keys)}, 联网搜索: {'启用' if self.openrouter_enable_search else '禁用'}")
        print(f"[INFO] Ollama服务: {'启用' if self.ollama_enabled else '禁用'}, 端点: {self.ollama_endpoint}, 模型: {self.ollama_model}")

        # 添加AI服务状态检查
        self._check_ai_services_status()
    
    def _check_ai_services_status(self):
        """检查AI服务状态"""
        print(f"[INFO] 🔄 AI服务状态检查:")
        
        # 检查Gemini服务
        if self.gemini_enabled:
            if self.gemini_api_keys:
                print(f"[INFO] ✅ Gemini服务: {len(self.gemini_api_keys)} 个API密钥可用")
            else:
                print(f"[WARN] ⚠️  Gemini服务: 已启用但无可用API密钥")
        else:
            print(f"[INFO] ⏸️  Gemini服务: 已禁用")

        # 检查OpenRouter服务
        if self.openrouter_enabled:
            if self.openrouter_api_keys:
                print(f"[INFO] ✅ OpenRouter服务: {len(self.openrouter_api_keys)} 个API密钥可用")
            else:
                print(f"[WARN] ⚠️  OpenRouter服务: 已启用但无可用API密钥")
        else:
            print(f"[INFO] ⏸️  OpenRouter服务: 已禁用")

        # 检查Ollama服务
        if self.ollama_enabled:
            try:
                import requests
                print(f"[INFO] 正在检查Ollama服务连接: {self.ollama_endpoint}")
                response = requests.get(f"{self.ollama_endpoint}/api/tags", timeout=5)
                if response.status_code == 200:
                    try:
                        tags_data = response.json()
                        models = [model.get('name', 'unknown') for model in tags_data.get('models', [])]
                        print(f"[INFO] ✅ Ollama服务: 连接正常，可用模型: {models}")
                        if self.ollama_model not in [m.split(':')[0] for m in models]:
                            print(f"[WARN] ⚠️  配置的模型 '{self.ollama_model}' 未在可用模型中找到")
                    except Exception as e:
                        print(f"[INFO] ✅ Ollama服务: 连接正常 (无法解析模型列表: {e})")
                else:
                    print(f"[WARN] ⚠️  Ollama服务: HTTP错误 {response.status_code}")
            except requests.exceptions.ConnectionError:
                print(f"[WARN] ⚠️  Ollama服务: 连接被拒绝 - 请检查:")
                print(f"[WARN]     1. Ollama是否在宿主机运行 (端口11434)")
                print(f"[WARN]     2. Docker是否能访问宿主机 (host.docker.internal)")
                print(f"[WARN]     3. 端点配置是否正确: {self.ollama_endpoint}")
            except requests.exceptions.Timeout:
                print(f"[WARN] ⚠️  Ollama服务: 连接超时 (5秒)")
            except Exception as e:
                print(f"[WARN] ⚠️  Ollama服务: 连接失败 - {type(e).__name__}: {str(e)}")
        else:
            print(f"[INFO] ⏸️  Ollama服务: 已禁用")

        # 检查是否至少有一个服务可用
        available_services = []
        if self.gemini_enabled and self.gemini_api_keys:
            available_services.append('gemini')
        if self.openrouter_enabled and self.openrouter_api_keys:
            available_services.append('openrouter')
        if self.ollama_enabled:
            available_services.append('ollama')

        if not available_services:
            print(f"[ERROR] ❌ 没有可用的AI服务！请检查服务开关和API密钥配置")
        else:
            print(f"[INFO] ✅ 可用AI服务: {available_services}, 首选项: {self.ai_preference}")
    def _get_random_gemini_api_key(self) -> str:
        """随机选择一个Gemini API密钥"""
        if not self.gemini_api_keys:
            return ""
        selected_key = random.choice(self.gemini_api_keys)
        print(f"[INFO] 随机选择Gemini API: {selected_key[:20]}...")
        return selected_key

    def _get_random_openrouter_api_key(self) -> str:
        """随机选择一个OpenRouter API密钥"""
        if not self.openrouter_api_keys:
            return ""
        selected_key = random.choice(self.openrouter_api_keys)
        print(f"[INFO] 随机选择OpenRouter API: {selected_key[:20]}...")
        return selected_key

    def _extract_json(self, text: str) -> Optional[Dict[str, Any]]:
        """去除AI响应中的代码块标记并解析JSON"""
        import re

        if not text or not isinstance(text, str):
            print(f"[ERROR] 无效的输入文本: {type(text)}")
            return None

        try:
            # 首先移除<think>标签及其内容
            cleaned = re.sub(r'<think>.*?</think>', '', text, flags=re.DOTALL)

            # 去除```json ... ```或``` ... ```包裹
            cleaned = re.sub(r'^```json|^```|```$', '', cleaned.strip(), flags=re.MULTILINE).strip()

            # 查找JSON对象的开始和结束位置
            start_idx = cleaned.find('{')

            if start_idx == -1:
                print(f"[WARN] 未找到JSON开始标记")
                return None

            # 从开始位置查找完整的JSON对象
            brace_count = 0
            end_idx = -1

            for i in range(start_idx, len(cleaned)):
                if cleaned[i] == '{':
                    brace_count += 1
                elif cleaned[i] == '}':
                    brace_count -= 1
                    if brace_count == 0:
                        end_idx = i
                        break

            if end_idx == -1:
                print(f"[WARN] 未找到完整的JSON对象（括号不匹配）")
                return None

            # 提取JSON部分
            json_str = cleaned[start_idx:end_idx + 1]

            result = json.loads(json_str)
            # 确保返回的是字典类型
            if isinstance(result, dict):
                print(f"[INFO] 成功解析JSON: {result}")
                return result
            else:
                print(f"[ERROR] JSON解析结果不是字典类型: {type(result)}")
                return None

        except json.JSONDecodeError as e:
            print(f"[ERROR] JSON解析失败: {e}")
            print(f"[ERROR] 尝试解析的JSON: {json_str if 'json_str' in locals() else 'N/A'}")
            return None

        except Exception as e:
            print(f"[ERROR] JSON提取异常: {e}")
            print(f"[ERROR] 原始文本: {text[:500]}...")
            return None



    def evaluate_news_relevance(self, news_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        评估新闻相关性

        Args:
            news_list: 新闻列表

        Returns:
            评估后的新闻列表
        """
        print(f"[INFO] 开始AI评估 {len(news_list)} 条新闻的相关性...")

        evaluated_news = []

        for i, news in enumerate(news_list, 1):
            print(f"[INFO] 评估第 {i}/{len(news_list)} 条新闻...")

            # 轮询机制：首选AI → 备选AI → 首选AI → 备选AI，总共4次
            result = None
            max_total_attempts = 4

            for attempt in range(max_total_attempts):
                # 轮询选择AI：偶数次(0,2)用首选，奇数次(1,3)用备选
                ai_index = attempt % len(self.ai_preference)
                ai_name = self.ai_preference[ai_index]

                # 计算这是该AI的第几次尝试
                ai_attempt_count = (attempt // len(self.ai_preference)) + 1

                # 添加延迟避免频繁调用（第一次不延迟）
                if attempt > 0:
                    delay_seconds = 2  # 2秒延迟
                    print(f"[INFO] 等待{delay_seconds}秒后进行下次尝试...")
                    time.sleep(delay_seconds)

                try:
                    # 检查服务是否启用
                    if ai_name == 'gemini' and not self.gemini_enabled:
                        print(f"[INFO] 跳过{ai_name.upper()}评估: 服务已禁用")
                        continue
                    elif ai_name == 'openrouter' and not self.openrouter_enabled:
                        print(f"[INFO] 跳过{ai_name.upper()}评估: 服务已禁用")
                        continue
                    elif ai_name == 'ollama' and not self.ollama_enabled:
                        print(f"[INFO] 跳过{ai_name.upper()}评估: 服务已禁用")
                        continue

                    print(f"[INFO] 轮询第{attempt+1}/4次: 尝试{ai_name.upper()}评估 (该AI第{ai_attempt_count}次)")

                    if ai_name == 'openrouter':
                        result = self._evaluate_with_openrouter(news)
                    elif ai_name == 'gemini':
                        result = self._evaluate_with_gemini(news)
                    elif ai_name == 'ollama':
                        result = self._evaluate_with_ollama(news)

                    if result:
                        print(f"[INFO] {ai_name.upper()}评估成功 (轮询第{attempt+1}次)")
                        break
                    else:
                        print(f"[WARN] {ai_name.upper()}评估失败 (轮询第{attempt+1}次)")

                except Exception as e:
                    print(f"[ERROR] {ai_name.upper()}评估异常 (轮询第{attempt+1}次): {e}")

            if result and isinstance(result, dict):
                # 添加评估结果到新闻
                score = result.get('relevance_score', 0.0)

                # 确保评分是数字类型
                if isinstance(score, str):
                    try:
                        score = float(score)
                    except (ValueError, TypeError):
                        score = 0.0

                # 兼容新旧两种格式
                is_relevant = result.get('is_relevant', score >= 5.0)  # 如果没有is_relevant字段，根据评分判断

                # 更新新闻数据
                news.update(result)
                news['relevance_score'] = score
                news['is_relevant'] = is_relevant
                evaluated_news.append(news)

                print(f"[INFO] 新闻 {i} 评估成功，评分: {score}")

            else:
                if result and not isinstance(result, dict):
                    print(f"[ERROR] 新闻 {i} AI评估返回了无效的结果类型: {type(result)}")
                print(f"[WARN] 新闻 {i} 所有AI评估都失败，跳过")

        print(f"[INFO] AI评估完成，{len(evaluated_news)}/{len(news_list)} 条新闻评估成功")
        return evaluated_news

    def optimize_and_translate_content(self, raw_content: str, original_link: str) -> Optional[Dict[str, Any]]:
        """
        优化和翻译内容

        Args:
            raw_content: 原始内容
            original_link: 原始链接

        Returns:
            优化后的内容字典
        """
        print(f"[INFO] 开始AI内容优化和翻译...")

        # 轮询机制：首选AI → 备选AI → 首选AI → 备选AI，总共4次
        max_total_attempts = 4

        for attempt in range(max_total_attempts):
            # 轮询选择AI：偶数次(0,2)用首选，奇数次(1,3)用备选
            ai_index = attempt % len(self.ai_preference)
            ai_name = self.ai_preference[ai_index]

            # 计算这是该AI的第几次尝试
            ai_attempt_count = (attempt // len(self.ai_preference)) + 1

            # 添加延迟避免频繁调用（第一次不延迟）
            if attempt > 0:
                delay_seconds = 2  # 2秒延迟
                print(f"[INFO] 等待{delay_seconds}秒后进行下次尝试...")
                time.sleep(delay_seconds)

            try:
                # 检查服务是否启用
                if ai_name == 'gemini' and not self.gemini_enabled:
                    print(f"[INFO] 跳过{ai_name.upper()}内容优化: 服务已禁用")
                    continue
                elif ai_name == 'openrouter' and not self.openrouter_enabled:
                    print(f"[INFO] 跳过{ai_name.upper()}内容优化: 服务已禁用")
                    continue
                elif ai_name == 'ollama' and not self.ollama_enabled:
                    print(f"[INFO] 跳过{ai_name.upper()}内容优化: 服务已禁用")
                    continue

                print(f"[INFO] 轮询第{attempt+1}/4次: 尝试{ai_name.upper()}内容优化 (该AI第{ai_attempt_count}次)")

                if ai_name == 'openrouter':
                    result = self._optimize_with_openrouter(raw_content, original_link)
                elif ai_name == 'gemini':
                    result = self._optimize_with_gemini(raw_content, original_link)
                elif ai_name == 'ollama':
                    result = self._optimize_with_ollama(raw_content, original_link)

                if result:
                    print(f"[INFO] {ai_name.upper()}内容优化成功 (轮询第{attempt+1}次)")
                    return result
                else:
                    print(f"[WARN] {ai_name.upper()}内容优化失败 (轮询第{attempt+1}次)")

            except Exception as e:
                print(f"[ERROR] {ai_name.upper()}内容优化异常 (轮询第{attempt+1}次): {e}")

        print(f"[ERROR] 所有AI内容优化都失败")
        return None

    def _get_content_for_evaluation(self, news: Dict[str, Any]) -> str:
        """获取用于AI评估的内容，按优先级选择可用字段"""
        # 优先级：content > description > summary > 空字符串
        content = news.get('content', '').strip()
        if content:
            print(f"[DEBUG] 使用content字段进行评估 (长度: {len(content)})")
            return content

        description = news.get('description', '').strip()
        if description:
            print(f"[DEBUG] 使用description字段进行评估 (长度: {len(description)})")
            return description

        summary = news.get('summary', '').strip()
        if summary:
            print(f"[DEBUG] 使用summary字段进行评估 (长度: {len(summary)})")
            return summary

        # 如果都没有内容，返回空字符串，让AI基于标题进行评估
        print(f"[WARN] 没有可用内容，仅基于标题进行AI评估")
        return ''

    def _evaluate_with_openrouter(self, news: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """使用OpenRouter评估新闻"""
        try:
            # 随机选择API密钥
            api_key = self._get_random_openrouter_api_key()
            if not api_key:
                print("[ERROR] 没有可用的OpenRouter API密钥")
                return None

            # 构建评估提示词 - 使用可用的内容字段
            content_for_evaluation = self._get_content_for_evaluation(news)
            prompt = self.evaluation_prompt.format(
                title=news.get('title', ''),
                content=content_for_evaluation,
                link=news.get('url', news.get('link', '')),
                pubDate=news.get('pubDate', news.get('published', ''))
            )

            # 构建请求数据
            request_data = {
                "model": self.openrouter_model,
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "temperature": 0.1,
                "max_tokens": 1000
            }

            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {api_key}"
            }

            response = requests.post(
                f"{self.openrouter_endpoint}/chat/completions",
                headers=headers,
                json=request_data,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                print(f"[DEBUG] OpenRouter评估响应: {result}")
                if 'choices' in result and len(result['choices']) > 0:
                    text = result['choices'][0]['message']['content']
                    return self._extract_json(text)
                else:
                    print(f"[ERROR] OpenRouter评估响应格式异常: {result}")
            else:
                print(f"[ERROR] OpenRouter评估API错误: {response.status_code} - {response.text}")

            return None

        except Exception as e:
            print(f"[ERROR] OpenRouter评估失败: {e}")
            return None



    def _evaluate_with_gemini(self, news: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """使用Gemini评估新闻"""
        try:
            # 随机选择API密钥
            api_key = self._get_random_gemini_api_key()
            if not api_key:
                print("[ERROR] 没有可用的Gemini API密钥")
                return None

            # 构建评估提示词 - 使用可用的内容字段
            content_for_evaluation = self._get_content_for_evaluation(news)
            prompt = self.evaluation_prompt.format(
                title=news.get('title', ''),
                content=content_for_evaluation,
                link=news.get('url', news.get('link', '')),
                pubDate=news.get('pubDate', news.get('published', ''))
            )

            # 构建请求数据
            request_data = {
                "contents": [
                    {
                        "parts": [
                            {
                                "text": prompt
                            }
                        ]
                    }
                ],
                "generationConfig": {
                    "temperature": 0.1,
                    "maxOutputTokens": 4000
                }
            }

            # 暂时禁用Gemini联网搜索，避免token超限
            # if self.gemini_enable_search:
            #     request_data["tools"] = [
            #         {
            #             "googleSearch": {}
            #         }
            #     ]

            url = f"https://generativelanguage.googleapis.com/v1beta/models/{self.gemini_model}:generateContent?key={api_key}"

            response = requests.post(
                url,
                headers={"Content-Type": "application/json"},
                json=request_data,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                print(f"[DEBUG] Gemini响应: {result}")
                if 'candidates' in result and len(result['candidates']) > 0:
                    candidate = result['candidates'][0]
                    if 'content' in candidate and 'parts' in candidate['content']:
                        text = candidate['content']['parts'][0]['text']
                        return self._extract_json(text)
                    else:
                        print(f"[ERROR] Gemini响应格式异常: {candidate}")
            else:
                print(f"[ERROR] Gemini API错误: {response.status_code} - {response.text}")

            return None

        except Exception as e:
            print(f"[ERROR] Gemini评估失败: {e}")
            return None

    def _optimize_with_openrouter(self, raw_content: str, original_link: str) -> Optional[Dict[str, Any]]:
        """使用OpenRouter优化内容"""
        try:
            # 随机选择API密钥
            api_key = self._get_random_openrouter_api_key()
            if not api_key:
                print("[ERROR] 没有可用的OpenRouter API密钥")
                return None

            # 构建优化提示词
            prompt = self.optimization_prompt.format(
                raw_content=raw_content,
                original_link=original_link,
                title=""  # 优化阶段通常没有原始标题
            )

            # 构建请求数据
            request_data = {
                "model": self.openrouter_model,
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "temperature": 0.1,
                "max_tokens": 4000
            }

            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {api_key}"
            }

            response = requests.post(
                f"{self.openrouter_endpoint}/chat/completions",
                headers=headers,
                json=request_data,
                timeout=60
            )

            if response.status_code == 200:
                result = response.json()
                print(f"[DEBUG] OpenRouter优化响应: {result}")
                if 'choices' in result and len(result['choices']) > 0:
                    text = result['choices'][0]['message']['content']
                    return self._extract_json(text)
                else:
                    print(f"[ERROR] OpenRouter优化响应格式异常: {result}")
            else:
                print(f"[ERROR] OpenRouter优化API错误: {response.status_code} - {response.text}")

            return None

        except Exception as e:
            print(f"[ERROR] OpenRouter优化失败: {e}")
            return None



    def _optimize_with_gemini(self, raw_content: str, original_link: str) -> Optional[Dict[str, Any]]:
        """使用Gemini优化内容"""
        try:
            # 随机选择API密钥
            api_key = self._get_random_gemini_api_key()
            if not api_key:
                print("[ERROR] 没有可用的Gemini API密钥")
                return None

            # 构建优化提示词
            prompt = self.optimization_prompt.format(
                raw_content=raw_content,
                original_link=original_link,
                title=""  # 优化阶段通常没有原始标题
            )

            # 构建请求数据
            request_data = {
                "contents": [
                    {
                        "parts": [
                            {
                                "text": prompt
                            }
                        ]
                    }
                ],
                "generationConfig": {
                    "temperature": 0.1,
                    "maxOutputTokens": 4000
                }
            }

            # 如果启用搜索，添加搜索工具
            if self.gemini_enable_search:
                request_data["tools"] = [
                    {
                        "googleSearch": {}
                    }
                ]

            url = f"https://generativelanguage.googleapis.com/v1beta/models/{self.gemini_model}:generateContent?key={api_key}"

            response = requests.post(
                url,
                headers={"Content-Type": "application/json"},
                json=request_data,
                timeout=60
            )

            if response.status_code == 200:
                result = response.json()
                print(f"[DEBUG] Gemini优化响应: {result}")
                if 'candidates' in result and len(result['candidates']) > 0:
                    candidate = result['candidates'][0]
                    if 'content' in candidate and 'parts' in candidate['content'] and len(candidate['content']['parts']) > 0:
                        text = candidate['content']['parts'][0]['text']
                        return self._extract_json(text)
                    else:
                        print(f"[ERROR] Gemini优化响应格式异常: {candidate}")
                else:
                    print(f"[ERROR] Gemini优化响应无candidates: {result}")
            else:
                print(f"[ERROR] Gemini优化API错误: {response.status_code} - {response.text}")

            return None

        except Exception as e:
            print(f"[ERROR] Gemini优化失败: {e}")
            return None

    def _evaluate_with_ollama(self, news: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """使用Ollama评估新闻"""
        try:
            # 构建评估提示词
            content_for_evaluation = self._get_content_for_evaluation(news)
            prompt = self.evaluation_prompt.format(
                title=news.get('title', ''),
                content=content_for_evaluation,
                link=news.get('url', news.get('link', '')),
                pubDate=news.get('pubDate', news.get('published', ''))
            )

            # 构建请求数据
            request_data = {
                "model": self.ollama_model,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.1,
                    "num_predict": 150  # 减少预测长度，强制简洁回答
                }
            }

            print(f"[DEBUG] 向Ollama发送评估请求: {self.ollama_endpoint}")
            response = requests.post(
                f"{self.ollama_endpoint}/api/generate",
                json=request_data,
                timeout=60
            )

            if response.status_code == 200:
                result = response.json()
                print(f"[DEBUG] Ollama评估响应: {result}")
                if 'response' in result:
                    text = result['response']
                    return self._extract_json(text)
                else:
                    print(f"[ERROR] Ollama评估响应格式异常: {result}")
            else:
                print(f"[ERROR] Ollama评估API错误: {response.status_code} - {response.text}")

            return None

        except requests.exceptions.ConnectionError:
            print(f"[ERROR] Ollama评估连接失败: 无法连接到 {self.ollama_endpoint}")
            print(f"[ERROR] 请确保Ollama服务正在运行并且网络配置正确")
            return None
        except requests.exceptions.Timeout:
            print(f"[ERROR] Ollama评估超时: 请求超过60秒未响应")
            return None
        except Exception as e:
            print(f"[ERROR] Ollama评估失败: {type(e).__name__}: {e}")
            return None

    def _optimize_with_ollama(self, raw_content: str, original_link: str) -> Optional[Dict[str, Any]]:
        """使用Ollama优化内容"""
        try:
            # 构建优化提示词
            prompt = self.optimization_prompt.format(
                raw_content=raw_content,
                original_link=original_link,
                title=""  # 优化阶段通常没有原始标题
            )

            # 构建请求数据
            request_data = {
                "model": self.ollama_model,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.1,
                    "num_predict": 2000
                }
            }

            print(f"[DEBUG] 向Ollama发送优化请求: {self.ollama_endpoint}")
            response = requests.post(
                f"{self.ollama_endpoint}/api/generate",
                json=request_data,
                timeout=120
            )

            if response.status_code == 200:
                result = response.json()
                print(f"[DEBUG] Ollama优化响应: {result}")
                if 'response' in result:
                    text = result['response']
                    return self._extract_json(text)
                else:
                    print(f"[ERROR] Ollama优化响应格式异常: {result}")
            else:
                print(f"[ERROR] Ollama优化API错误: {response.status_code} - {response.text}")

            return None

        except requests.exceptions.ConnectionError:
            print(f"[ERROR] Ollama优化连接失败: 无法连接到 {self.ollama_endpoint}")
            print(f"[ERROR] 请确保Ollama服务正在运行并且网络配置正确")
            return None
        except requests.exceptions.Timeout:
            print(f"[ERROR] Ollama优化超时: 请求超过120秒未响应")
            return None
        except Exception as e:
            print(f"[ERROR] Ollama优化失败: {type(e).__name__}: {e}")
            return None