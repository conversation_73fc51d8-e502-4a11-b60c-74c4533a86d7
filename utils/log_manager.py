#!/usr/bin/env python3
"""
日志管理工具
用于清理和管理应用程序日志文件
"""

import os
import glob
import time
from datetime import datetime, timedelta


class LogManager:
    """日志管理器"""
    
    def __init__(self, log_dir="/app/logs"):
        self.log_dir = log_dir
        self.max_log_age_days = 7  # 保留7天的日志
        self.max_log_size_mb = 50  # 单个日志文件最大50MB
    
    def cleanup_old_logs(self):
        """清理过期的日志文件"""
        try:
            if not os.path.exists(self.log_dir):
                return
            
            cutoff_time = time.time() - (self.max_log_age_days * 24 * 3600)
            cleaned_count = 0
            
            # 查找所有日志文件
            log_patterns = [
                os.path.join(self.log_dir, "*.log"),
                os.path.join(self.log_dir, "*.log.*"),
            ]
            
            for pattern in log_patterns:
                for log_file in glob.glob(pattern):
                    try:
                        # 检查文件修改时间
                        if os.path.getmtime(log_file) < cutoff_time:
                            os.remove(log_file)
                            cleaned_count += 1
                            print(f"[INFO] 删除过期日志文件: {log_file}")
                    except Exception as e:
                        print(f"[WARN] 删除日志文件失败 {log_file}: {e}")
            
            if cleaned_count > 0:
                print(f"[INFO] 清理完成，删除了 {cleaned_count} 个过期日志文件")
            else:
                print(f"[INFO] 没有需要清理的过期日志文件")
                
        except Exception as e:
            print(f"[ERROR] 日志清理失败: {e}")
    
    def rotate_large_logs(self):
        """轮转过大的日志文件"""
        try:
            if not os.path.exists(self.log_dir):
                return
            
            rotated_count = 0
            max_size_bytes = self.max_log_size_mb * 1024 * 1024
            
            # 检查主要日志文件
            main_logs = [
                os.path.join(self.log_dir, "cron.log"),
                os.path.join(self.log_dir, "cron_check.log"),
            ]
            
            for log_file in main_logs:
                if os.path.exists(log_file):
                    try:
                        if os.path.getsize(log_file) > max_size_bytes:
                            # 创建轮转文件名
                            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                            rotated_name = f"{log_file}.{timestamp}"
                            
                            # 移动当前日志文件
                            os.rename(log_file, rotated_name)
                            
                            # 创建新的空日志文件
                            with open(log_file, 'w') as f:
                                f.write(f"# 日志轮转于 {datetime.now()}\n")
                            
                            rotated_count += 1
                            print(f"[INFO] 轮转大日志文件: {log_file} -> {rotated_name}")
                            
                    except Exception as e:
                        print(f"[WARN] 轮转日志文件失败 {log_file}: {e}")
            
            if rotated_count > 0:
                print(f"[INFO] 日志轮转完成，轮转了 {rotated_count} 个文件")
                
        except Exception as e:
            print(f"[ERROR] 日志轮转失败: {e}")
    
    def get_log_statistics(self):
        """获取日志统计信息"""
        try:
            if not os.path.exists(self.log_dir):
                return {"total_files": 0, "total_size_mb": 0}
            
            total_files = 0
            total_size = 0
            
            for root, dirs, files in os.walk(self.log_dir):
                for file in files:
                    if file.endswith('.log') or '.log.' in file:
                        file_path = os.path.join(root, file)
                        try:
                            total_size += os.path.getsize(file_path)
                            total_files += 1
                        except Exception:
                            pass
            
            return {
                "total_files": total_files,
                "total_size_mb": round(total_size / (1024 * 1024), 2)
            }
            
        except Exception as e:
            print(f"[ERROR] 获取日志统计失败: {e}")
            return {"total_files": 0, "total_size_mb": 0}
    
    def cleanup_all(self):
        """执行完整的日志清理"""
        print(f"[INFO] 开始日志清理...")
        
        # 获取清理前的统计
        before_stats = self.get_log_statistics()
        print(f"[INFO] 清理前: {before_stats['total_files']} 个文件, {before_stats['total_size_mb']} MB")
        
        # 执行清理
        self.cleanup_old_logs()
        self.rotate_large_logs()
        
        # 获取清理后的统计
        after_stats = self.get_log_statistics()
        print(f"[INFO] 清理后: {after_stats['total_files']} 个文件, {after_stats['total_size_mb']} MB")
        
        saved_mb = before_stats['total_size_mb'] - after_stats['total_size_mb']
        if saved_mb > 0:
            print(f"[INFO] 节省磁盘空间: {saved_mb} MB")


def main():
    """主函数"""
    log_manager = LogManager()
    log_manager.cleanup_all()


if __name__ == "__main__":
    main()
