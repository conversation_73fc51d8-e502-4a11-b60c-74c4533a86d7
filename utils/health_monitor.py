#!/usr/bin/env python3
"""
健康监控器 - 提供系统健康状态检查和监控功能
"""

import os
import sys
import json
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.filesystem_guardian import FileSystemGuardian
from utils.config_manager import ConfigManager

class HealthMonitor:
    """系统健康监控器"""
    
    def __init__(self, base_path: str = "/app"):
        self.base_path = base_path
        self.guardian = FileSystemGuardian(base_path)
        self.config_manager = ConfigManager(os.path.join(base_path, 'config.yaml'))
    
    def check_filesystem_health(self) -> Dict[str, Any]:
        """检查文件系统健康状态"""
        try:
            results = self.guardian.comprehensive_check()
            return {
                'status': 'healthy' if results['overall_status'] in ['healthy', 'repaired'] else 'unhealthy',
                'details': results,
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def check_config_health(self) -> Dict[str, Any]:
        """检查配置健康状态"""
        try:
            config = self.config_manager.load_config()
            
            # 检查关键配置项
            required_keys = [
                'daily_run_time', 'timezone', 'max_send_limit',
                'ai_preference', 'rss_settings'
            ]
            
            missing_keys = []
            for key in required_keys:
                if key not in config:
                    missing_keys.append(key)
            
            # 检查环境变量
            env_vars = ['GEMINI_API_KEYS', 'OPENROUTER_API_KEYS', 'WEBHOOK_URL']
            missing_env_vars = []
            for var in env_vars:
                if not os.getenv(var):
                    missing_env_vars.append(var)
            
            status = 'healthy'
            if missing_keys or missing_env_vars:
                status = 'warning'
            
            return {
                'status': status,
                'config_keys_count': len(config),
                'missing_config_keys': missing_keys,
                'missing_env_vars': missing_env_vars,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def check_last_run_status(self) -> Dict[str, Any]:
        """检查最后一次运行状态"""
        try:
            last_run_file = os.path.join(self.base_path, 'data', 'last_run_date')
            
            if not os.path.exists(last_run_file):
                return {
                    'status': 'no_runs',
                    'message': '尚未执行过任务',
                    'timestamp': datetime.now().isoformat()
                }
            
            # 获取最后运行时间
            last_run_time = datetime.fromtimestamp(os.path.getmtime(last_run_file))
            time_since_last_run = datetime.now() - last_run_time
            
            # 判断状态
            if time_since_last_run > timedelta(hours=25):  # 超过25小时未运行
                status = 'stale'
            elif time_since_last_run > timedelta(hours=1):  # 1小时前运行过
                status = 'healthy'
            else:  # 最近1小时内运行过
                status = 'recent'
            
            return {
                'status': status,
                'last_run_time': last_run_time.isoformat(),
                'hours_since_last_run': time_since_last_run.total_seconds() / 3600,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def check_log_health(self) -> Dict[str, Any]:
        """检查日志健康状态"""
        try:
            log_dir = os.path.join(self.base_path, 'logs')
            
            if not os.path.exists(log_dir):
                return {
                    'status': 'no_logs',
                    'message': '日志目录不存在',
                    'timestamp': datetime.now().isoformat()
                }
            
            # 统计日志文件
            log_files = []
            total_size = 0
            
            for filename in os.listdir(log_dir):
                if filename.endswith('.log'):
                    filepath = os.path.join(log_dir, filename)
                    if os.path.isfile(filepath):
                        size = os.path.getsize(filepath)
                        mtime = datetime.fromtimestamp(os.path.getmtime(filepath))
                        log_files.append({
                            'name': filename,
                            'size_bytes': size,
                            'last_modified': mtime.isoformat()
                        })
                        total_size += size
            
            return {
                'status': 'healthy',
                'log_files_count': len(log_files),
                'total_size_bytes': total_size,
                'total_size_mb': round(total_size / 1024 / 1024, 2),
                'log_files': log_files,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def comprehensive_health_check(self) -> Dict[str, Any]:
        """综合健康检查"""
        start_time = time.time()
        
        health_report = {
            'overall_status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'checks': {}
        }
        
        # 执行各项检查
        checks = {
            'filesystem': self.check_filesystem_health,
            'config': self.check_config_health,
            'last_run': self.check_last_run_status,
            'logs': self.check_log_health
        }
        
        unhealthy_checks = []
        warning_checks = []
        
        for check_name, check_func in checks.items():
            try:
                result = check_func()
                health_report['checks'][check_name] = result
                
                if result['status'] in ['error', 'unhealthy', 'stale']:
                    unhealthy_checks.append(check_name)
                elif result['status'] == 'warning':
                    warning_checks.append(check_name)
                    
            except Exception as e:
                health_report['checks'][check_name] = {
                    'status': 'error',
                    'error': f'检查执行失败: {str(e)}',
                    'timestamp': datetime.now().isoformat()
                }
                unhealthy_checks.append(check_name)
        
        # 确定整体状态
        if unhealthy_checks:
            health_report['overall_status'] = 'unhealthy'
            health_report['unhealthy_checks'] = unhealthy_checks
        elif warning_checks:
            health_report['overall_status'] = 'warning'
            health_report['warning_checks'] = warning_checks
        
        # 添加执行时间
        health_report['check_duration_seconds'] = round(time.time() - start_time, 3)
        
        return health_report
    
    def save_health_report(self, report: Dict[str, Any]) -> str:
        """保存健康报告到文件"""
        try:
            reports_dir = os.path.join(self.base_path, 'data', 'health_reports')
            os.makedirs(reports_dir, exist_ok=True)
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_file = os.path.join(reports_dir, f'health_report_{timestamp}.json')
            
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            return report_file
            
        except Exception as e:
            raise Exception(f"保存健康报告失败: {e}")


def main():
    """命令行接口"""
    import sys
    
    monitor = HealthMonitor()
    
    if len(sys.argv) > 1:
        check_type = sys.argv[1]
        
        if check_type == 'filesystem':
            result = monitor.check_filesystem_health()
        elif check_type == 'config':
            result = monitor.check_config_health()
        elif check_type == 'last_run':
            result = monitor.check_last_run_status()
        elif check_type == 'logs':
            result = monitor.check_log_health()
        else:
            result = monitor.comprehensive_health_check()
    else:
        result = monitor.comprehensive_health_check()
    
    # 输出结果
    print(json.dumps(result, indent=2, ensure_ascii=False))
    
    # 保存报告
    try:
        report_file = monitor.save_health_report(result)
        print(f"\n健康报告已保存: {report_file}", file=sys.stderr)
    except Exception as e:
        print(f"\n保存健康报告失败: {e}", file=sys.stderr)
    
    # 设置退出码
    if result.get('overall_status') == 'healthy':
        sys.exit(0)
    elif result.get('overall_status') == 'warning':
        sys.exit(1)
    else:
        sys.exit(2)


if __name__ == '__main__':
    main()
